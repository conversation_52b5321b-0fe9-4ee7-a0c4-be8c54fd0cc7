package com.vusecurity.auth.shared.infrastructure.security;

import com.vusecurity.multitenant.MultitenantConfiguration;
import com.vusecurity.multitenant.TenantContext;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.http.HttpStatus;
import org.springframework.web.filter.GenericFilterBean;

import java.io.IOException;

public class FilterMultitenantSecurity extends GenericFilterBean {

    private static final Logger logger = LoggerFactory.getLogger(FilterMultitenantSecurity.class);

    private final MultitenantConfiguration multitenantConfiguration;

    public FilterMultitenantSecurity(MultitenantConfiguration multitenantConfiguration) {
        this.multitenantConfiguration = multitenantConfiguration;
    }

    @Override
    public void destroy() {
        super.destroy();
        TenantContext.clear();
        MDC.clear();
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {

        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;

        String requestURI = request.getRequestURI().substring(request.getContextPath().length());

        // Extract tenant ID from the configured header
        String tenantId = request.getHeader(multitenantConfiguration.getIdHeader());

        if (tenantId != null && !tenantId.isEmpty()) {
            MDC.put("tenant", tenantId);
            TenantContext.setCurrentTenant(tenantId);
            logger.trace("TenantId: {} extracted from header: {}", tenantId, multitenantConfiguration.getIdHeader());
        } else {
            // Fallback to public tenant when header is missing
            MDC.put("tenant", "public");
            TenantContext.setCurrentTenant("public");
            logger.trace("No tenant header found, using public tenant. Header expected: {}", multitenantConfiguration.getIdHeader());
        }

        filterChain.doFilter(request, response);
    }
}
