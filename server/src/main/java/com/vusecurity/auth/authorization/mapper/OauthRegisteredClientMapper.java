package com.vusecurity.auth.authorization.mapper;

import java.time.Duration;
import java.util.Arrays;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.ClientAuthenticationMethod;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClient;
import org.springframework.security.oauth2.server.authorization.settings.ClientSettings;
import org.springframework.security.oauth2.server.authorization.settings.TokenSettings;

import com.vusecurity.auth.authorization.dto.ClientSettingsDto;
import com.vusecurity.auth.authorization.dto.TokenSettingsDto;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.Oauth2RegisteredClient;

public class OauthRegisteredClientMapper {
    private static final Logger logger = LoggerFactory.getLogger(OauthRegisteredClientMapper.class);

    // Private constructor to prevent instantiation
    private OauthRegisteredClientMapper() {
        throw new UnsupportedOperationException("Utility class");
    }

    /**
     * Converts Spring OAuth2 ClientSettings to ClientSettingsDto
     */
    private static ClientSettingsDto toClientSettingsDto(ClientSettings clientSettings) {
        if (clientSettings == null) {
            return ClientSettingsDto.withDefaults();
        }

        ClientSettingsDto dto = new ClientSettingsDto();

        // Use direct method calls instead of settings map for better type safety
        dto.setRequireAuthorizationConsent(clientSettings.isRequireAuthorizationConsent());
        dto.setRequireProofKey(clientSettings.isRequireProofKey());
        dto.setJwkSetUrl(clientSettings.getJwkSetUrl());
        // Note: These methods might not be available in all versions, using defaults for now
        dto.setTokenEndpointAuthenticationSigningAlgorithm(null); // Will use default
        dto.setX509CertificateSubjectDN(clientSettings.getX509CertificateSubjectDN());

        return dto;
    }

    /**
     * Converts ClientSettingsDto to Spring OAuth2 ClientSettings
     */
    private static ClientSettings toClientSettings(ClientSettingsDto dto) {
        if (dto == null) {
            return ClientSettings.builder().build();
        }

        ClientSettings.Builder builder = ClientSettings.builder();

        if (dto.getRequireAuthorizationConsent() != null) {
            builder.requireAuthorizationConsent(dto.getRequireAuthorizationConsent());
        }
        if (dto.getRequireProofKey() != null) {
            builder.requireProofKey(dto.getRequireProofKey());
        }
        if (dto.getJwkSetUrl() != null) {
            builder.jwkSetUrl(dto.getJwkSetUrl());
        }
        // Note: tokenEndpointAuthenticationSigningAlgorithm requires JwsAlgorithm enum
        // For now, we'll skip this field or implement proper enum conversion later
        if (dto.getX509CertificateSubjectDN() != null) {
            builder.x509CertificateSubjectDN(dto.getX509CertificateSubjectDN());
        }

        return builder.build();
    }

    /**
     * Converts Spring OAuth2 TokenSettings to TokenSettingsDto
     */
    private static TokenSettingsDto toTokenSettingsDto(TokenSettings tokenSettings) {
        if (tokenSettings == null) {
            return TokenSettingsDto.withDefaults();
        }

        TokenSettingsDto dto = new TokenSettingsDto();

        // Use direct method calls for better type safety
        Duration accessTokenTtl = tokenSettings.getAccessTokenTimeToLive();
        if (accessTokenTtl != null) {
            dto.setAccessTokenTimeToLive((int) accessTokenTtl.getSeconds());
        }

        // Note: Some methods might not be available, using defaults
        dto.setAccessTokenFormat("self-contained"); // Default format
        dto.setReuseRefreshTokens(tokenSettings.isReuseRefreshTokens());

        Duration refreshTokenTtl = tokenSettings.getRefreshTokenTimeToLive();
        if (refreshTokenTtl != null) {
            dto.setRefreshTokenTimeToLive((int) refreshTokenTtl.getSeconds());
        }

        dto.setIdTokenSignatureAlgorithm("RS256"); // Default algorithm
        dto.setX509CertificateBoundAccessTokens(tokenSettings.isX509CertificateBoundAccessTokens());

        Duration authCodeTtl = tokenSettings.getAuthorizationCodeTimeToLive();
        if (authCodeTtl != null) {
            dto.setAuthorizationCodeTimeToLive((int) authCodeTtl.getSeconds());
        }

        Duration deviceCodeTtl = tokenSettings.getDeviceCodeTimeToLive();
        if (deviceCodeTtl != null) {
            dto.setDeviceCodeTimeToLive((int) deviceCodeTtl.getSeconds());
        }

        return dto;
    }

    /**
     * Converts TokenSettingsDto to Spring OAuth2 TokenSettings
     */
    private static TokenSettings toTokenSettings(TokenSettingsDto dto) {
        if (dto == null) {
            return TokenSettings.builder().build();
        }

        TokenSettings.Builder builder = TokenSettings.builder();

        if (dto.getAccessTokenTimeToLive() != null) {
            builder.accessTokenTimeToLive(Duration.ofSeconds(dto.getAccessTokenTimeToLive()));
        }
        if (dto.getReuseRefreshTokens() != null) {
            builder.reuseRefreshTokens(dto.getReuseRefreshTokens());
        }
        if (dto.getRefreshTokenTimeToLive() != null) {
            builder.refreshTokenTimeToLive(Duration.ofSeconds(dto.getRefreshTokenTimeToLive()));
        }
        if (dto.getX509CertificateBoundAccessTokens() != null) {
            builder.x509CertificateBoundAccessTokens(dto.getX509CertificateBoundAccessTokens());
        }
        if (dto.getAuthorizationCodeTimeToLive() != null) {
            builder.authorizationCodeTimeToLive(Duration.ofSeconds(dto.getAuthorizationCodeTimeToLive()));
        }
        if (dto.getDeviceCodeTimeToLive() != null) {
            builder.deviceCodeTimeToLive(Duration.ofSeconds(dto.getDeviceCodeTimeToLive()));
        }

        return builder.build();
    }

    public static Oauth2RegisteredClient toEntity(RegisteredClient client) {
        logger.debug("Converting RegisteredClient to Oauth2RegisteredClient: {}", client.getId());
        Oauth2RegisteredClient entity = new Oauth2RegisteredClient();
        entity.setId(client.getId());
        entity.setClientId(client.getClientId());
        entity.setClientIdIssuedAt(client.getClientIdIssuedAt());
        entity.setClientSecret(client.getClientSecret());
        entity.setClientSecretExpiresAt(client.getClientSecretExpiresAt());
        entity.setClientName(client.getClientName());
        entity.setClientAuthenticationMethods(
                client.getClientAuthenticationMethods().stream()
                        .map(ClientAuthenticationMethod::getValue)
                        .collect(Collectors.joining(",")));
        entity.setAuthorizationGrantTypes(
                client.getAuthorizationGrantTypes().stream()
                        .map(AuthorizationGrantType::getValue)
                        .collect(Collectors.joining(",")));
        entity.setRedirectUris(String.join(",", client.getRedirectUris()));
        entity.setPostLogoutRedirectUris(String.join(",", client.getPostLogoutRedirectUris()));
        entity.setScopes(String.join(",", client.getScopes()));
        // Convert Spring OAuth2 settings to DTOs
        logger.trace("Converting clientSettings and tokenSettings to DTOs for RegisteredClient: {}", client.getId());
        entity.setClientSettings(toClientSettingsDto(client.getClientSettings()));
        entity.setTokenSettings(toTokenSettingsDto(client.getTokenSettings()));
        return entity;
    }

    public static RegisteredClient toRegisteredClient(Oauth2RegisteredClient entity) {
        logger.debug("Converting Oauth2RegisteredClient to RegisteredClient: {}", entity.getId());
        RegisteredClient.Builder builder = RegisteredClient.withId(entity.getId())
                .clientId(entity.getClientId())
                .clientIdIssuedAt(entity.getClientIdIssuedAt())
                .clientSecret(entity.getClientSecret())
                .clientSecretExpiresAt(entity.getClientSecretExpiresAt())
                .clientName(entity.getClientName());

        Arrays.stream(entity.getClientAuthenticationMethods().split(","))
                .forEach(method -> builder.clientAuthenticationMethod(new ClientAuthenticationMethod(method)));

        Arrays.stream(entity.getAuthorizationGrantTypes().split(","))
                .forEach(grant -> builder.authorizationGrantType(new AuthorizationGrantType(grant)));

        if (entity.getRedirectUris() != null && !entity.getRedirectUris().isEmpty()) {
            Arrays.stream(entity.getRedirectUris().split(",")).forEach(builder::redirectUri);
        }
        if (entity.getPostLogoutRedirectUris() != null && !entity.getPostLogoutRedirectUris().isEmpty()) {
            Arrays.stream(entity.getPostLogoutRedirectUris().split(",")).forEach(builder::postLogoutRedirectUri);
        }
        Arrays.stream(entity.getScopes().split(",")).forEach(builder::scope);

        // Convert DTOs to Spring OAuth2 settings
        logger.trace("Converting DTOs to clientSettings and tokenSettings for Oauth2RegisteredClient: {}",
                entity.getId());
        builder.clientSettings(toClientSettings(entity.getClientSettings()));
        builder.tokenSettings(toTokenSettings(entity.getTokenSettings()));

        return builder.build();
    }

}
