package com.vusecurity.auth.authorization.application.handler;

import com.vusecurity.auth.authorization.application.exception.GroupNotFoundException;
import com.vusecurity.auth.authorization.application.query.GetGroupQuery;
import com.vusecurity.auth.authorization.application.query.GetGroupsPagedQuery;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.AccountGroupJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountGroupRepository;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class GetGroupHandler implements GetGroupQuery {

    private static final Logger logger = LoggerFactory.getLogger(GetGroupHandler.class);

    private final AccountGroupRepository accountGroupRepository;

    @Override
    public AccountGroupJpaEntity getGroupById(UUID groupId) {
        logger.debug("Getting group by ID: {}", groupId);

        return accountGroupRepository.findById(groupId)
                .orElseThrow(() -> new GroupNotFoundException("Group not found with ID: " + groupId));
    }

    @Override
    public Page<AccountGroupJpaEntity> getAllGroups(GetGroupsPagedQuery query) {
        logger.debug("Getting all groups with query: {}", query);

        try {
            Sort sort = Sort.by(Sort.Direction.fromString(query.getSortDirection()), "name");
            if (query.getSortBy() != null && !query.getSortBy().trim().isEmpty()) {
                sort = Sort.by(Sort.Direction.fromString(query.getSortDirection()), query.getSortBy());
            }

            Pageable pageable = PageRequest.of(query.getPage() - 1, query.getPageSize(), sort);

            if (query.getFilter() != null && !query.getFilter().trim().isEmpty()) {
                Specification<AccountGroupJpaEntity> specification = createFilterSpecification(query.getFilter().trim());
                return accountGroupRepository.findAll(specification, pageable);
            } else {
                return accountGroupRepository.findAll(pageable);
            }

        } catch (Exception e) {
            logger.error("Error getting groups: {}", e.getMessage());
            throw e;
        }
    }

    private Specification<AccountGroupJpaEntity> createFilterSpecification(String filter) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // Filter by name (case-insensitive)
            predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("name")),
                    "%" + filter.toLowerCase() + "%"
            ));

            // Filter by description (case-insensitive)
            if (root.get("description").getJavaType() == String.class) {
                predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("description")),
                        "%" + filter.toLowerCase() + "%"
                ));
            }

            return criteriaBuilder.or(predicates.toArray(new Predicate[0]));
        };
    }
} 