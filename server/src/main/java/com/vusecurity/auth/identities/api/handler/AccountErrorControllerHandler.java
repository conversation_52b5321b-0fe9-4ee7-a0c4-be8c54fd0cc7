package com.vusecurity.auth.identities.api.handler;


import com.vusecurity.auth.identities.application.exception.AccountMetadataParseException;
import com.vusecurity.core.commons.ApiMessage;
import com.vusecurity.auth.identities.application.exception.AccountException;
import com.vusecurity.auth.identities.application.exception.AccountNotFoundException;
import com.vusecurity.auth.identities.application.exception.AccountAlreadyExistsException;
import com.vusecurity.auth.identities.application.exception.IdentityProviderNotFoundException;
import com.vusecurity.auth.identities.application.exception.PrimaryClaimValueOutOfBoundsException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.server.ResponseStatusException;

@ControllerAdvice(assignableTypes = {com.vusecurity.auth.identities.api.controller.AccountsController.class})
@Order(Ordered.HIGHEST_PRECEDENCE)
public class AccountErrorControllerHandler {

    private static final Logger logger = LoggerFactory.getLogger(AccountErrorControllerHandler.class);

    /**
     * Handle ResponseStatusException thrown by account controllers.
     * This ensures that the HTTP status code from the ResponseStatusException is preserved.
     */
    @ExceptionHandler(ResponseStatusException.class)
    public ResponseEntity<ApiMessage> handleResponseStatusException(ResponseStatusException ex) {
        logger.debug("Handling ResponseStatusException: {} - {}", ex.getStatusCode(), ex.getReason());

        // Extract the status code and reason from the ResponseStatusException
        int statusCode = ex.getStatusCode().value();
        String message = ex.getReason() != null ? ex.getReason() : ex.getMessage();

        // Create appropriate error code based on status
        int errorCode = switch (statusCode) {
            case 400 -> 4300; // Bad Request
            case 404 -> 4304; // Not Found
            case 409 -> 4305; // Conflict
            case 500 -> 4306; // Internal Server Error
            default -> 4307; // Generic error
        };

        ApiMessage error = new ApiMessage(errorCode, message);
        return ResponseEntity.status(ex.getStatusCode()).body(error);
    }

    @ExceptionHandler(AccountMetadataParseException.class)
    public ResponseEntity<ApiMessage> handleMetadataParseException(AccountMetadataParseException ex) {

        ApiMessage error = new ApiMessage(4301, ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(error);
    }

    @ExceptionHandler(AccountNotFoundException.class)
    public ResponseEntity<ApiMessage> handleNotFoundException(AccountNotFoundException ex) {

        ApiMessage error = new ApiMessage(4302, ex.getMessage());
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(error);
    }

    @ExceptionHandler(AccountException.class)
    public ResponseEntity<ApiMessage> handleAccountException(AccountException ex) {

        ApiMessage error = new ApiMessage(4303, ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(error);
    }

    @ExceptionHandler(AccountAlreadyExistsException.class)
    public ResponseEntity<ApiMessage> handleAlreadyExistsException(AccountAlreadyExistsException ex) {
        ApiMessage error = new ApiMessage(4304, ex.getMessage());
        return ResponseEntity.status(HttpStatus.CONFLICT).body(error);
    }

    @ExceptionHandler(IdentityProviderNotFoundException.class)
    public ResponseEntity<ApiMessage> handleIdentityProviderNotFoundException(IdentityProviderNotFoundException ex) {
        ApiMessage error = new ApiMessage(4305, ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(error);
    }

    @ExceptionHandler(PrimaryClaimValueOutOfBoundsException.class)
    public ResponseEntity<ApiMessage> handlePrimaryClaimValueOutOfBoundsException(PrimaryClaimValueOutOfBoundsException ex) {
        ApiMessage error = new ApiMessage(4306, ex.getMessage());
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(error);
    }
}
