package com.vusecurity.auth.contracts.enums;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.*;

class DataTypeEnumTest {

    @Test
    void string_shouldAcceptAnyValue() {
        assertTrue(DataTypeEnum.STRING.isValid("any string"));
        assertTrue(DataTypeEnum.STRING.isValid(""));
        assertTrue(DataTypeEnum.STRING.isValid("special!@#$%^&*()chars"));
        assertTrue(DataTypeEnum.STRING.isValid("123"));
        assertTrue(DataTypeEnum.STRING.isValid("multi\nline\nstring"));
    }

    @Test
    void string_shouldTrimWhitespace() {
        assertEquals("test", DataTypeEnum.STRING.normalize("  test  "));
        assertEquals("", DataTypeEnum.STRING.normalize("   "));
        assertEquals("multi word", DataTypeEnum.STRING.normalize("  multi word  "));
    }

    @ParameterizedTest
    @ValueSource(strings = {"123", "123.45", "0", "-123", "+123", "123.0", "1.23e10", "1.23E-10", "1.23e+10"})
    void numeric_shouldAcceptValidNumbers(String value) {
        assertTrue(DataTypeEnum.NUMERIC.isValid(value));
    }

    @ParameterizedTest
    @ValueSource(strings = {"abc", "123abc", "1.2.3", "1..2", "e10", "1.23e", "1.23e10.5", ""})
    void numeric_shouldRejectInvalidNumbers(String value) {
        assertFalse(DataTypeEnum.NUMERIC.isValid(value));
    }

    @Test
    void numeric_shouldNormalizeValues() {
        assertEquals("123", DataTypeEnum.NUMERIC.normalize("123.00"));
        assertEquals("123.45", DataTypeEnum.NUMERIC.normalize("123.45000"));
        assertEquals("0", DataTypeEnum.NUMERIC.normalize("0.00"));
        assertEquals("-123", DataTypeEnum.NUMERIC.normalize("-123.00"));
        assertEquals("1.23E+10", DataTypeEnum.NUMERIC.normalize("1.23e10"));
    }

    @ParameterizedTest
    @ValueSource(strings = {"true", "TRUE", "True", "false", "FALSE", "False", "1", "0"})
    void bool_shouldAcceptValidBooleans(String value) {
        assertTrue(DataTypeEnum.BOOL.isValid(value));
    }

    @ParameterizedTest
    @ValueSource(strings = {"yes", "no", "2", "-1", "True1", "False0", ""})
    void bool_shouldRejectInvalidBooleans(String value) {
        assertFalse(DataTypeEnum.BOOL.isValid(value));
    }

    @Test
    void bool_shouldNormalizeToLowercase() {
        assertEquals("true", DataTypeEnum.BOOL.normalize("true"));
        assertEquals("true", DataTypeEnum.BOOL.normalize("TRUE"));
        assertEquals("true", DataTypeEnum.BOOL.normalize("True"));
        assertEquals("true", DataTypeEnum.BOOL.normalize("1"));
        assertEquals("false", DataTypeEnum.BOOL.normalize("false"));
        assertEquals("false", DataTypeEnum.BOOL.normalize("FALSE"));
        assertEquals("false", DataTypeEnum.BOOL.normalize("False"));
        assertEquals("false", DataTypeEnum.BOOL.normalize("0"));
    }

    @ParameterizedTest
    @ValueSource(strings = {"2023-01-01", "2023-12-31", "2000-02-29", "1999-02-28"})
    void date_shouldAcceptValidDates(String value) {
        assertTrue(DataTypeEnum.DATE.isValid(value));
    }

    @ParameterizedTest
    @ValueSource(strings = {"2023-13-01", "2023-01-32", "2023-02-29", "23-01-01", "2023/01/01", "01-01-2023", ""})
    void date_shouldRejectInvalidDates(String value) {
        assertFalse(DataTypeEnum.DATE.isValid(value));
    }

    @Test
    void date_shouldNormalizeToIsoFormat() {
        assertEquals("2023-01-01", DataTypeEnum.DATE.normalize("2023-01-01"));
        assertEquals("2023-12-31", DataTypeEnum.DATE.normalize("2023-12-31"));
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "2023-01-01T10:30:00Z",
        "2023-01-01T10:30:00.123Z",
        "2023-01-01T10:30:00+02:00",
        "2023-01-01T10:30:00.123+02:00",
        "2023-01-01T10:30:00-05:00"
    })
    void datetime_shouldAcceptValidDateTimes(String value) {
        assertTrue(DataTypeEnum.DATETIME.isValid(value));
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "2023-01-01 10:30:00",
        "2023-01-01T10:30:00",
        "2023-01-01T25:30:00Z",
        "2023-01-01T10:60:00Z",
        "2023-01-01T10:30:60Z",
        ""
    })
    void datetime_shouldRejectInvalidDateTimes(String value) {
        assertFalse(DataTypeEnum.DATETIME.isValid(value));
    }

    @Test
    void datetime_shouldNormalizeToIsoFormat() {
        String input = "2023-01-01T10:30:00Z";
        String normalized = DataTypeEnum.DATETIME.normalize(input);
        assertTrue(normalized.contains("2023-01-01T10:30"));
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==",
        "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAA==",
        "https://example.com/image.png",
        "https://example.com/image.jpg",
        "https://example.com/image.jpeg",
        "https://example.com/image.gif",
        "https://example.com/image.bmp"
    })
    void image_shouldAcceptValidImages(String value) {
        assertTrue(DataTypeEnum.IMAGE.isValid(value));
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "not-an-image",
        "data:text/plain;base64,SGVsbG8gV29ybGQ=",
        "https://example.com/document.pdf",
        "https://example.com/image.txt",
        "data:image/png;base64,invalid-base64-!@#$",
        ""
    })
    void image_shouldRejectInvalidImages(String value) {
        assertFalse(DataTypeEnum.IMAGE.isValid(value));
    }

    @Test
    void image_shouldTrimWhitespace() {
        String input = "  https://example.com/image.png  ";
        assertEquals("https://example.com/image.png", DataTypeEnum.IMAGE.normalize(input));
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "[\"item1\", \"item2\"]",
        "item1,item2,item3",
        "[1, 2, 3]",
        "a,b,c",
        "single,item"
    })
    void array_shouldAcceptValidArrays(String value) {
        assertTrue(DataTypeEnum.ARRAY.isValid(value));
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "single-item-no-comma-or-brackets",
        "item1;item2",
        ""
    })
    void array_shouldRejectInvalidArrays(String value) {
        assertFalse(DataTypeEnum.ARRAY.isValid(value));
    }

    @Test
    void array_shouldTrimWhitespace() {
        String input = "  [\"item1\", \"item2\"]  ";
        assertEquals("[\"item1\", \"item2\"]", DataTypeEnum.ARRAY.normalize(input));
    }

    @Test
    void allDataTypes_shouldHaveValidationMethod() {
        for (DataTypeEnum type : DataTypeEnum.values()) {
            assertNotNull(type, "DataTypeEnum should not be null");
            
            // Test that validation method exists and can be called
            assertDoesNotThrow(() -> type.isValid("test"));
            assertDoesNotThrow(() -> type.normalize("test"));
        }
    }

    @Test
    void string_shouldHandleSpecialCharacters() {
        String specialChars = "!@#$%^&*()_+-=[]{}|;':\",./<>?`~";
        assertTrue(DataTypeEnum.STRING.isValid(specialChars));
        assertEquals(specialChars, DataTypeEnum.STRING.normalize(specialChars));
    }

    @Test
    void numeric_shouldHandleEdgeCases() {
        assertTrue(DataTypeEnum.NUMERIC.isValid("0"));
        assertTrue(DataTypeEnum.NUMERIC.isValid("0.0"));
        assertTrue(DataTypeEnum.NUMERIC.isValid("-0"));
        assertTrue(DataTypeEnum.NUMERIC.isValid("+0"));
        
        assertEquals("0", DataTypeEnum.NUMERIC.normalize("0"));
        assertEquals("0", DataTypeEnum.NUMERIC.normalize("0.0"));
        assertEquals("0", DataTypeEnum.NUMERIC.normalize("-0"));
        assertEquals("0", DataTypeEnum.NUMERIC.normalize("+0"));
    }

    @Test
    void bool_shouldHandleEdgeCases() {
        assertFalse(DataTypeEnum.BOOL.isValid("t"));
        assertFalse(DataTypeEnum.BOOL.isValid("f"));
        assertFalse(DataTypeEnum.BOOL.isValid("T"));
        assertFalse(DataTypeEnum.BOOL.isValid("F"));
        assertFalse(DataTypeEnum.BOOL.isValid("TRUE1"));
        assertFalse(DataTypeEnum.BOOL.isValid("1true"));
    }

    @Test
    void date_shouldHandleLeapYears() {
        assertTrue(DataTypeEnum.DATE.isValid("2000-02-29")); // Leap year
        assertTrue(DataTypeEnum.DATE.isValid("2004-02-29")); // Leap year
        assertFalse(DataTypeEnum.DATE.isValid("2001-02-29")); // Not a leap year
        assertFalse(DataTypeEnum.DATE.isValid("1900-02-29")); // Not a leap year (century)
    }

    @Test
    void datetime_shouldHandleTimezones() {
        assertTrue(DataTypeEnum.DATETIME.isValid("2023-01-01T10:30:00Z"));
        assertTrue(DataTypeEnum.DATETIME.isValid("2023-01-01T10:30:00+00:00"));
        assertTrue(DataTypeEnum.DATETIME.isValid("2023-01-01T10:30:00-05:00"));
        assertTrue(DataTypeEnum.DATETIME.isValid("2023-01-01T10:30:00+14:00"));
        assertFalse(DataTypeEnum.DATETIME.isValid("2023-01-01T10:30:00+25:00")); // Invalid timezone
    }

    @Test
    void image_shouldHandleDifferentFormats() {
        // Base64 formats
        assertTrue(DataTypeEnum.IMAGE.isValid("data:image/png;base64,ABCD"));
        assertTrue(DataTypeEnum.IMAGE.isValid("data:image/jpeg;base64,ABCD"));
        assertTrue(DataTypeEnum.IMAGE.isValid("data:image/gif;base64,ABCD"));
        
        // URL formats
        assertTrue(DataTypeEnum.IMAGE.isValid("https://example.com/image.png"));
        assertTrue(DataTypeEnum.IMAGE.isValid("http://example.com/image.jpg"));
        
        // Invalid formats
        assertFalse(DataTypeEnum.IMAGE.isValid("ftp://example.com/image.png"));
        assertFalse(DataTypeEnum.IMAGE.isValid("data:text/plain;base64,ABC123"));
    }

    @Test
    void array_shouldHandleEmptyArrays() {
        assertTrue(DataTypeEnum.ARRAY.isValid("[]"));
        assertTrue(DataTypeEnum.ARRAY.isValid(","));
        assertFalse(DataTypeEnum.ARRAY.isValid(""));
    }
}