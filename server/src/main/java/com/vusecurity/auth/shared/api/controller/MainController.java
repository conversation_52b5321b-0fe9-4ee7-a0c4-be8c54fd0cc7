package com.vusecurity.auth.shared.api.controller;

import com.vusecurity.auth.contracts.api.v1.dto.shared.SetupAdminUserResponse;
import com.vusecurity.auth.shared.infrastructure.migration.initial.SetupAdminUserService;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

@RestController
@RequiredArgsConstructor
@SecurityRequirement(name = "oauth2 client credentials")
@Tag(name = "System", description = "System information and health operations")
public class MainController {

    private final SetupAdminUserService setupAdminUserService;;

    @Operation(
            summary = "Create admin user",
            description = "Retrieves the user admin created for system use"
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "200",
                    description = "Admin user created successfully",
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SetupAdminUserResponse.class,
                                    example = "{\"accountId\": \"123e4567-e89b-12d3-a456-************\", \"email\": \"<EMAIL>\", \"password\": \"Password123\"}")
                    )
            )
    })
    @GetMapping("/initialize")
    public SetupAdminUserResponse initialize() {
        return setupAdminUserService.start();
    }
}