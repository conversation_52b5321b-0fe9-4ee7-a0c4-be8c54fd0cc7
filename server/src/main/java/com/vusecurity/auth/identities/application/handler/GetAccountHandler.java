package com.vusecurity.auth.identities.application.handler;

import com.vusecurity.auth.identities.application.query.GetAccountQuery;
import com.vusecurity.auth.identities.application.query.GetAccountsByIdentityQuery;
import com.vusecurity.auth.identities.application.query.GetAccountsPagedQuery;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityProviderJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountRepository;
import com.vusecurity.business.domain.Business;

import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.identities.application.exception.AccountNotFoundException;

@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class GetAccountHandler implements GetAccountQuery {

    private static final Logger logger = LoggerFactory.getLogger(GetAccountHandler.class);
    
    private final AccountRepository accountRepository;

    @Override
    public AccountJpaEntity getAccountById(String accountId) {
        logger.debug("Getting account by ID: {}", accountId);
        
        UUID uuid;
        try {
            uuid = UUID.fromString(accountId);
        } catch (IllegalArgumentException e) {
            logger.error("Invalid account ID dataFormat: {}", accountId);
            throw new IllegalArgumentException("Invalid account ID dataFormat: " + accountId, e);
        }
        
        return accountRepository.findById(uuid)
                .orElseThrow(() -> {
                    logger.error("Account not found with ID: {}", accountId);
                    return new AccountNotFoundException(uuid);
                });
    }

    @Override
    public Page<AccountJpaEntity> getAllAccounts(GetAccountsPagedQuery query) {
        logger.debug("Getting all accounts with query: {}", query);
        
        // Convert 1-based page to 0-based for Spring Data
        int pageNumber = Math.max(0, query.getPage() - 1);
        Pageable pageable = PageRequest.of(pageNumber, query.getPageSize());
        
        // For now, we'll return all accounts. In the future, we can add filtering based on query.getFilter()
        Page<AccountJpaEntity> accounts = accountRepository.findAll(pageable);
        
        logger.debug("Found {} accounts", accounts.getTotalElements());
        return accounts;
    }

    @Override
    public Page<AccountJpaEntity> getAccountsByIdentity(GetAccountsByIdentityQuery query) {
        logger.debug("Getting accounts by identity ID: {}", query.getIdentityId());
        
        // Convert 1-based page to 0-based for Spring Data
        int pageNumber = Math.max(0, query.getPage() - 1);
        Pageable pageable = PageRequest.of(pageNumber, query.getPageSize());
        
        Page<AccountJpaEntity> accounts = accountRepository.findByIdentityId(query.getIdentityId(), pageable);
        
        logger.debug("Found {} accounts for identity {}", accounts.getTotalElements(), query.getIdentityId());
        return accounts;
    }

    @Override
    public List<UUID> filterUUIDsByBusiness(UUID businessId, List<UUID> idsToCheck){
        return accountRepository.filterByBusiness(businessId, idsToCheck);
    }

    @Override
    public Page<AccountJpaEntity> getAllAccountsByBusinessidAndProviderId(UUID businessId, UUID providerId, Specification<AccountJpaEntity> spec, Pageable page){
            Specification<AccountJpaEntity> businessSpec = (root, query, cb) -> {
            Join<AccountJpaEntity, Business> businessJoin = root.join("business");
            Join<AccountJpaEntity, IdentityProviderJpaEntity> providerJoin = root.join("identityProvider");
            
            Predicate joinCondition = cb.and(
                cb.equal(businessJoin.get("id"), businessId),
                cb.equal(providerJoin.get("id"), providerId)
            );
            
            //Join the logic union Account <-> ClaimValue as it can't be easely mapped.
            //Only if there's a specification, to avoid querying extra data.
            if (spec != null) {
                Predicate dynamicPredicate = spec.toPredicate(root, query, cb);
                return cb.and(joinCondition, dynamicPredicate);
            }

            return joinCondition;
        };

        return accountRepository.findAll(businessSpec, page);
    }

    //? Should we move this to SCIM?
    @Override
    public Page<AccountJpaEntity> getAllAccountsBySpecs(UUID businessId, UUID providerId, Specification<AccountJpaEntity> dynamicSpec, Pageable page) {
        
        Specification<AccountJpaEntity> businessSpec = (root, query, cb) -> {
            query.distinct(true);

            Join<AccountJpaEntity, Business> businessJoin = root.join("business");
            Join<AccountJpaEntity, IdentityProviderJpaEntity> providerJoin = root.join("identityProvider");
            
            Predicate joinCondition = cb.and(
                cb.equal(businessJoin.get("id"), businessId),
                cb.equal(providerJoin.get("id"), providerId)
            );
            
            //Join the logic union Account <-> ClaimValue as it can't be easely mapped.
            //Only if there's a specification, to avoid querying extra data.
            if (dynamicSpec != null) {
                Root<ClaimValueJpaEntity> claimValue = query.from(ClaimValueJpaEntity.class);

                joinCondition = cb.and(
                    cb.equal(root.get("id"), claimValue.get("ownerId")),
                    cb.equal(claimValue.get("ownerType"), "ACCOUNT")
                );

                Predicate dynamicCondition = dynamicSpec.toPredicate(root, query, cb);
                return cb.and(joinCondition, dynamicCondition);
            }

            return joinCondition;
        };
        
        return accountRepository.findAll(businessSpec, page);
    }
}