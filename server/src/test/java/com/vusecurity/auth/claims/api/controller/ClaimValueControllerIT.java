package com.vusecurity.auth.claims.api.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetDefinitionMappingJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetDefinitionMappingRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimValueRepository;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import com.vusecurity.auth.contracts.enums.OwnerType;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityProviderJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.IdentityRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.IdentityProviderRepository;
import com.vusecurity.auth.shared.TestDataBuilder;
import com.vusecurity.auth.shared.test.BaseIntegrationTest;
import com.vusecurity.business.domain.Business;
import com.vusecurity.business.domain.repositories.BusinessRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.UUID;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for ClaimValueController covering all endpoints and edge cases.
 * Tests include validation of array parsing functionality for ARRAY type claims.
 */
@AutoConfigureWebMvc
class ClaimValueControllerIT extends BaseIntegrationTest {

    @LocalServerPort
    int port;

    @Autowired
    WebApplicationContext webApplicationContext;

    @Autowired
    BusinessRepository businessRepo;

    @Autowired
    AccountRepository accountRepo;

    @Autowired
    IdentityRepository identityRepo;

    @Autowired
    IdentityProviderRepository identityProviderRepo;

    @Autowired
    ClaimDefinitionRepository claimDefinitionRepo;

    @Autowired
    ClaimSetRepository claimSetRepo;

    @Autowired
    ClaimSetDefinitionMappingRepository mappingRepo;

    @Autowired
    ClaimValueRepository claimValueRepo;

    @Autowired
    ObjectMapper objectMapper;

    private MockMvc mockMvc;

    // Test data
    private Business testBusiness;
    private AccountJpaEntity testAccount;
    private ClaimDefinitionJpaEntity stringClaimDef;
    private ClaimDefinitionJpaEntity arrayClaimDef;
    private ClaimDefinitionJpaEntity phoneNumberClaimDef;
    private ClaimDefinitionJpaEntity phoneNumbersClaimDef;
    private ClaimSetJpaEntity testClaimSet;
    private ClaimValueJpaEntity stringClaimValue;
    private ClaimValueJpaEntity arrayClaimValue;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // Clear all data for clean slate (order matters for foreign keys)
        claimValueRepo.deleteAll();
        mappingRepo.deleteAll();
        claimSetRepo.deleteAll();
        claimDefinitionRepo.deleteAll();
        accountRepo.deleteAll();
        identityRepo.deleteAll();
        identityProviderRepo.deleteAll();
        businessRepo.deleteAll();

        // Create test business
        testBusiness = TestDataBuilder.validBusiness()
                .name("Test Business")
                .build();
        testBusiness = businessRepo.save(testBusiness);

        // Create test account with all dependencies step by step
        // First save identity
        IdentityJpaEntity identity = TestDataBuilder.validIdentity()
                .name("Test Identity")
                .build();
        identity = identityRepo.save(identity);

        // Create identity provider
        IdentityProviderJpaEntity identityProvider = new IdentityProviderJpaEntity();
        identityProvider.setId(UUID.randomUUID());
        identityProvider.setName("Test Identity Provider");
        identityProvider.setDescription("Test identity provider for integration tests");
        identityProvider = identityProviderRepo.save(identityProvider);

        // Now create account with saved dependencies
        testAccount = new AccountJpaEntity(testBusiness, identity, identityProvider, AccountType.WORKFORCE);
        testAccount = accountRepo.save(testAccount);

        // Create string claim definition
        stringClaimDef = TestDataBuilder.validClaimDefinition()
                .code("email")
                .name("Email Address")
                .description("Email address")
                .dataType(DataTypeEnum.STRING)
                .dataFormat("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")
                .build();
        stringClaimDef = claimDefinitionRepo.save(stringClaimDef);

        // Create array claim definition
        arrayClaimDef = TestDataBuilder.validClaimDefinition()
                .code("tags")
                .name("Tags")
                .description("List of tags")
                .dataType(DataTypeEnum.ARRAY)
                .dataFormat("^\\[.*\\]$")
                .build();
        arrayClaimDef = claimDefinitionRepo.save(arrayClaimDef);

        // Create phone number claim definition (for UUID reference test)
        phoneNumberClaimDef = TestDataBuilder.validClaimDefinition()
                .code("phone_number")
                .name("Phone Number")
                .description("Individual phone number")
                .dataType(DataTypeEnum.STRING)
                .dataFormat("^\\+?[1-9]\\d{1,14}$")
                .build();
        phoneNumberClaimDef = claimDefinitionRepo.save(phoneNumberClaimDef);

        // Create phone numbers array claim definition with UUID reference
        phoneNumbersClaimDef = TestDataBuilder.validClaimDefinition()
                .code("phone_numbers")
                .name("Phone Numbers")
                .description("List of phone numbers")
                .dataType(DataTypeEnum.ARRAY)
                .dataFormat(phoneNumberClaimDef.getId().toString())
                .build();
        phoneNumbersClaimDef = claimDefinitionRepo.save(phoneNumbersClaimDef);

        // Create test claim set
        testClaimSet = TestDataBuilder.validClaimSet()
                .business(testBusiness)
                .accountType(AccountType.WORKFORCE)
                .name("Test ClaimSet")
                .build();
        testClaimSet = claimSetRepo.save(testClaimSet);

        // Create claim set mappings
        ClaimSetDefinitionMappingJpaEntity stringMapping = new ClaimSetDefinitionMappingJpaEntity();
        stringMapping.setClaimSet(testClaimSet);
        stringMapping.setClaimDefinition(stringClaimDef);
        stringMapping.setEnforceUniqueness(false);
        stringMapping.setClaimDefinitionOrder(1);
        mappingRepo.save(stringMapping);

        ClaimSetDefinitionMappingJpaEntity arrayMapping = new ClaimSetDefinitionMappingJpaEntity();
        arrayMapping.setClaimSet(testClaimSet);
        arrayMapping.setClaimDefinition(arrayClaimDef);
        arrayMapping.setEnforceUniqueness(false);
        arrayMapping.setClaimDefinitionOrder(2);
        mappingRepo.save(arrayMapping);

        ClaimSetDefinitionMappingJpaEntity phoneNumbersMapping = new ClaimSetDefinitionMappingJpaEntity();
        phoneNumbersMapping.setClaimSet(testClaimSet);
        phoneNumbersMapping.setClaimDefinition(phoneNumbersClaimDef);
        phoneNumbersMapping.setEnforceUniqueness(false);
        phoneNumbersMapping.setClaimDefinitionOrder(3);
        mappingRepo.save(phoneNumbersMapping);

        // Create test claim values
        stringClaimValue = TestDataBuilder.validClaimValue()
                .claimDefinition(stringClaimDef)
                .ownerId(testAccount.getId())
                .ownerType(OwnerType.ACCOUNT)
                .value("<EMAIL>")
                .isPrimary(true)
                .source("USER_INPUT")
                .build();
        stringClaimValue.setClaimSetClaimValue(new ClaimSetClaimValueJpaEntity(testClaimSet, stringClaimValue));
        stringClaimValue = claimValueRepo.save(stringClaimValue);

        arrayClaimValue = TestDataBuilder.validClaimValue()
                .claimDefinition(arrayClaimDef)
                .ownerId(testAccount.getId())
                .ownerType(OwnerType.ACCOUNT)
                .value("[\"tag1\", \"tag2\", \"tag3\"]")
                .isPrimary(false)
                .source("SYSTEM")
                .build();
        arrayClaimValue.setClaimSetClaimValue(new ClaimSetClaimValueJpaEntity(testClaimSet, arrayClaimValue));
        arrayClaimValue = claimValueRepo.save(arrayClaimValue);
    }

    @Test
    void should_get_all_claim_values_with_pagination() throws Exception {
        mockMvc.perform(get("/api/v1/claim-values")
                        .param("page", "1")
                        .param("pageSize", "10")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.currentPage").value(1))
                .andExpect(jsonPath("$.pageSize").value(10))
                .andExpect(jsonPath("$.totalElements").value(2))
                .andExpect(jsonPath("$.content.length()").value(2));
    }

    @Test
    void should_get_claim_value_by_id_with_string_value() throws Exception {
        mockMvc.perform(get("/api/v1/claim-values/{id}", stringClaimValue.getId())
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value(stringClaimValue.getId().toString()))
                .andExpect(jsonPath("$.claimDefinitionId").value(stringClaimDef.getId().toString()))
                .andExpect(jsonPath("$.ownerType").value("ACCOUNT"))
                .andExpect(jsonPath("$.ownerId").value(testAccount.getId().toString()))
                .andExpect(jsonPath("$.value").value("<EMAIL>"))
                .andExpect(jsonPath("$.primary").value(true))
                .andExpect(jsonPath("$.source").value("USER_INPUT"));
    }

    @Test
    void should_get_claim_value_by_id_with_parsed_array_value() throws Exception {
        mockMvc.perform(get("/api/v1/claim-values/{id}", arrayClaimValue.getId())
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value(arrayClaimValue.getId().toString()))
                .andExpect(jsonPath("$.claimDefinitionId").value(arrayClaimDef.getId().toString()))
                .andExpect(jsonPath("$.ownerType").value("ACCOUNT"))
                .andExpect(jsonPath("$.ownerId").value(testAccount.getId().toString()))
                .andExpect(jsonPath("$.value").isArray())
                .andExpect(jsonPath("$.value.length()").value(3))
                .andExpect(jsonPath("$.value[0]").value("tag1"))
                .andExpect(jsonPath("$.value[1]").value("tag2"))
                .andExpect(jsonPath("$.value[2]").value("tag3"))
                .andExpect(jsonPath("$.primary").value(false))
                .andExpect(jsonPath("$.source").value("SYSTEM"));
    }

    @Test
    void should_get_claim_value_by_business_id() throws Exception {
        mockMvc.perform(get("/api/v1/claim-values/{id}/business/{businessId}",
                        stringClaimValue.getId(), testBusiness.getId())
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value(stringClaimValue.getId().toString()))
                .andExpect(jsonPath("$.value").value("<EMAIL>"));
    }

    @Test
    void should_search_claim_values_by_value() throws Exception {
        mockMvc.perform(get("/api/v1/claim-values/search")
                        .param("page", "1")
                        .param("pageSize", "10")
                        .param("value", "<EMAIL>")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.totalElements").value(1))
                .andExpect(jsonPath("$.content[0].value").value("<EMAIL>"));
    }

    @Test
    void should_create_new_string_claim_value() throws Exception {
        String requestBody = """
                {
                    "claimSetId": "%s",
                    "claimDefinitionId": "%s",
                    "accountId": "%s",
                    "value": "<EMAIL>",
                    "primary": false,
                    "computed": false,
                    "source": "USER_INPUT"
                }
                """.formatted(testClaimSet.getId(),stringClaimDef.getId(), testAccount.getId());

        mockMvc.perform(post("/api/v1/claim-values")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestBody)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isCreated())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.claimDefinitionId").value(stringClaimDef.getId().toString()))
                .andExpect(jsonPath("$.value").value("<EMAIL>"))
                .andExpect(jsonPath("$.primary").value(false))
                .andExpect(jsonPath("$.source").value("USER_INPUT"));
    }

    @Test
    void should_create_new_array_claim_value_with_uuid_reference() throws Exception {
        String requestBody = """
                {
                    "claimSetId": "%s",
                    "claimDefinitionId": "%s",
                    "accountId": "%s",
                    "value": "[\\\"+***********\\\", \\\"+***********\\\"]",
                    "primary": true,
                    "computed": false,
                    "source": "USER_INPUT"
                }
                """.formatted(testClaimSet.getId(),phoneNumbersClaimDef.getId(), testAccount.getId());

        mockMvc.perform(post("/api/v1/claim-values")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestBody)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isCreated())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.claimDefinitionId").value(phoneNumbersClaimDef.getId().toString()))
                .andExpect(jsonPath("$.value").isArray())
                .andExpect(jsonPath("$.value.length()").value(2))
                .andExpect(jsonPath("$.value[0]").value("+***********"))
                .andExpect(jsonPath("$.value[1]").value("+***********"))
                .andExpect(jsonPath("$.primary").value(true));
    }

    @Test
    void should_update_claim_value() throws Exception {
        String requestBody = """
                {
                    "value": "<EMAIL>",
                    "primary": false,
                    "source": "ADMIN_UPDATE"
                }
                """;

        mockMvc.perform(patch("/api/v1/claim-values/{id}", stringClaimValue.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestBody)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isNoContent());

        // Verify the update
        mockMvc.perform(get("/api/v1/claim-values/{id}", stringClaimValue.getId())
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.value").value("<EMAIL>"))
                .andExpect(jsonPath("$.primary").value(false))
                .andExpect(jsonPath("$.source").value("ADMIN_UPDATE"));
    }

    @Test
    void should_delete_claim_value() throws Exception {
        mockMvc.perform(delete("/api/v1/claim-values/{id}", arrayClaimValue.getId()))
                .andExpect(status().isNoContent());

        // Verify deletion
        mockMvc.perform(get("/api/v1/claim-values/{id}", arrayClaimValue.getId())
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }

    @Test
    void should_return_404_for_non_existent_claim_value() throws Exception {
        UUID nonExistentId = UUID.randomUUID();
        
        mockMvc.perform(get("/api/v1/claim-values/{id}", nonExistentId)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }

    @Test
    void should_handle_malformed_json_in_array_value() throws Exception {
        // Create a claim value with invalid JSON
        ClaimValueJpaEntity malformedClaimValue = TestDataBuilder.validClaimValue()
                .claimDefinition(arrayClaimDef)
                .ownerId(testAccount.getId())
                .ownerType(OwnerType.ACCOUNT)
                .value("invalid-json-format")
                .build();
        malformedClaimValue = claimValueRepo.save(malformedClaimValue);

        // Should fallback to string value
        mockMvc.perform(get("/api/v1/claim-values/{id}", malformedClaimValue.getId())
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.value").value("invalid-json-format"))
                .andExpect(jsonPath("$.value").isString());
    }

    @Test
    void should_handle_empty_array_value() throws Exception {
        // Create a claim value with empty array
        ClaimValueJpaEntity emptyArrayClaimValue = TestDataBuilder.validClaimValue()
                .claimDefinition(arrayClaimDef)
                .ownerId(testAccount.getId())
                .ownerType(OwnerType.ACCOUNT)
                .value("[]")
                .build();
        emptyArrayClaimValue = claimValueRepo.save(emptyArrayClaimValue);

        mockMvc.perform(get("/api/v1/claim-values/{id}", emptyArrayClaimValue.getId())
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.value").isArray())
                .andExpect(jsonPath("$.value.length()").value(0));
    }

    @Test
    void should_handle_null_claim_value() throws Exception {
        // Create a claim value with null value
        ClaimValueJpaEntity nullClaimValue = TestDataBuilder.validClaimValue()
                .claimDefinition(stringClaimDef)
                .ownerId(testAccount.getId())
                .ownerType(OwnerType.ACCOUNT)
                .value(null)
                .build();
        nullClaimValue = claimValueRepo.save(nullClaimValue);

        mockMvc.perform(get("/api/v1/claim-values/{id}", nullClaimValue.getId())
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.value").doesNotExist());
    }

    @Test
    void should_validate_required_fields_in_create_request() throws Exception {
        String invalidRequestBody = """
                {
                    "value": "some-value"
                }
                """;

        mockMvc.perform(post("/api/v1/claim-values")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(invalidRequestBody)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    void should_reject_invalid_uuid_reference_in_array_claim() throws Exception {
        // Try to create array claim value with elements that don't match referenced pattern
        String requestBody = """
                {
                    "claimDefinitionId": "%s",
                    "accountId": "%s",
                    "value": "[\\\"+***********\\\", \\\"invalid-phone\\\"]",
                    "primary": false,
                    "computed": false,
                    "source": "USER_INPUT"
                }
                """.formatted(phoneNumbersClaimDef.getId(), testAccount.getId());

        mockMvc.perform(post("/api/v1/claim-values")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestBody)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    void should_handle_complex_array_values() throws Exception {
        // Create a claim value with complex array structure
        String complexArray = "[\"123 Main St, City, State 12345\", \"456 Oak Ave, Town, State 67890\"]";
        
        ClaimValueJpaEntity complexClaimValue = TestDataBuilder.validClaimValue()
                .claimDefinition(arrayClaimDef)
                .ownerId(testAccount.getId())
                .ownerType(OwnerType.ACCOUNT)
                .value(complexArray)
                .build();
        complexClaimValue = claimValueRepo.save(complexClaimValue);

        mockMvc.perform(get("/api/v1/claim-values/{id}", complexClaimValue.getId())
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.value").isArray())
                .andExpect(jsonPath("$.value.length()").value(2))
                .andExpect(jsonPath("$.value[0]").value("123 Main St, City, State 12345"))
                .andExpect(jsonPath("$.value[1]").value("456 Oak Ave, Town, State 67890"));
    }

    @Test
    void should_verify_pagination_with_multiple_pages() throws Exception {
        // Create additional claim values to test pagination
        for (int i = 0; i < 15; i++) {
            ClaimValueJpaEntity additionalValue = TestDataBuilder.validClaimValue()
                    .claimDefinition(stringClaimDef)
                    .ownerId(testAccount.getId())
                    .ownerType(OwnerType.ACCOUNT)
                    .value("test" + i + "@example.com")
                    .build();
            claimValueRepo.save(additionalValue);
        }

        // Test first page
        mockMvc.perform(get("/api/v1/claim-values")
                        .param("page", "1")
                        .param("pageSize", "10")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.currentPage").value(1))
                .andExpect(jsonPath("$.pageSize").value(10))
                .andExpect(jsonPath("$.totalElements").value(17)) // 2 existing + 15 new
                .andExpect(jsonPath("$.content.length()").value(10));

        // Test second page
        mockMvc.perform(get("/api/v1/claim-values")
                        .param("page", "2")
                        .param("pageSize", "10")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.currentPage").value(2))
                .andExpect(jsonPath("$.pageSize").value(10))
                .andExpect(jsonPath("$.totalElements").value(17))
                .andExpect(jsonPath("$.content.length()").value(7));
    }

    @Test
    void should_get_related_claim_values_by_search_value() throws Exception {
        // Create hierarchical test data for address scenario
        // ClaimSet for "direccion_ana_casa"
        ClaimSetJpaEntity casaClaimSet = TestDataBuilder.validClaimSet()
                .business(testBusiness)
                .accountType(AccountType.EMPLOYEE)
                .isIdentifier(false)
                .name("direccion_ana_casa")
                .description("Casa address for Ana")
                .build();
        casaClaimSet = claimSetRepo.save(casaClaimSet);

        // ClaimSet for "direccion_ana_trabajo"
        ClaimSetJpaEntity trabajoClaimSet = TestDataBuilder.validClaimSet()
                .business(testBusiness)
                .accountType(AccountType.EMPLOYEE)
                .isIdentifier(false)
                .name("direccion_ana_trabajo")
                .description("Work address for Ana")
                .build();
        trabajoClaimSet = claimSetRepo.save(trabajoClaimSet);

        // Create ClaimDefinitions for address components
        ClaimDefinitionJpaEntity tipoClaimDef = TestDataBuilder.validClaimDefinition()
                .code("tipo")
                .name("Address Type")
                .dataType(DataTypeEnum.STRING)
                .build();
        tipoClaimDef = claimDefinitionRepo.save(tipoClaimDef);

        ClaimDefinitionJpaEntity calleClaimDef = TestDataBuilder.validClaimDefinition()
                .code("calle")
                .name("Street")
                .dataType(DataTypeEnum.STRING)
                .build();
        calleClaimDef = claimDefinitionRepo.save(calleClaimDef);

        ClaimDefinitionJpaEntity ciudadClaimDef = TestDataBuilder.validClaimDefinition()
                .code("ciudad")
                .name("City")
                .dataType(DataTypeEnum.STRING)
                .build();
        ciudadClaimDef = claimDefinitionRepo.save(ciudadClaimDef);

        // Create ClaimValues for Casa address
        ClaimValueJpaEntity tipoCasaValue = TestDataBuilder.validClaimValue()
                .claimDefinition(tipoClaimDef)
                .ownerId(testAccount.getId())
                .ownerType(OwnerType.ACCOUNT)
                .value("Casa")
                .build();
        tipoCasaValue.setClaimSetClaimValue(new ClaimSetClaimValueJpaEntity(casaClaimSet, tipoCasaValue));
        tipoCasaValue = claimValueRepo.save(tipoCasaValue);

        ClaimValueJpaEntity calleCasaValue = TestDataBuilder.validClaimValue()
                .claimDefinition(calleClaimDef)
                .ownerId(testAccount.getId())
                .ownerType(OwnerType.ACCOUNT)
                .value("Calle 123")
                .build();
        calleCasaValue.setClaimSetClaimValue(new ClaimSetClaimValueJpaEntity(casaClaimSet, calleCasaValue));
        calleCasaValue = claimValueRepo.save(calleCasaValue);

        ClaimValueJpaEntity ciudadCasaValue = TestDataBuilder.validClaimValue()
                .claimDefinition(ciudadClaimDef)
                .ownerId(testAccount.getId())
                .ownerType(OwnerType.ACCOUNT)
                .value("Madrid")
                .build();
        ciudadCasaValue.setClaimSetClaimValue(new ClaimSetClaimValueJpaEntity(casaClaimSet, ciudadCasaValue));
        ciudadCasaValue = claimValueRepo.save(ciudadCasaValue);

        // Create ClaimValues for Trabajo address
        ClaimValueJpaEntity tipoTrabajoValue = TestDataBuilder.validClaimValue()
                .claimDefinition(tipoClaimDef)
                .ownerId(testAccount.getId())
                .ownerType(OwnerType.ACCOUNT)
                .value("Trabajo")
                .build();
        tipoTrabajoValue.setClaimSetClaimValue(new ClaimSetClaimValueJpaEntity(trabajoClaimSet, tipoTrabajoValue));
        tipoTrabajoValue = claimValueRepo.save(tipoTrabajoValue);

        ClaimValueJpaEntity calleTrabajoValue = TestDataBuilder.validClaimValue()
                .claimDefinition(calleClaimDef)
                .ownerId(testAccount.getId())
                .ownerType(OwnerType.ACCOUNT)
                .value("Av. Principal 456")
                .build();
        calleTrabajoValue.setClaimSetClaimValue(new ClaimSetClaimValueJpaEntity(trabajoClaimSet, calleTrabajoValue));
        calleTrabajoValue = claimValueRepo.save(calleTrabajoValue);

        ClaimValueJpaEntity ciudadTrabajoValue = TestDataBuilder.validClaimValue()
                .claimDefinition(ciudadClaimDef)
                .ownerId(testAccount.getId())
                .ownerType(OwnerType.ACCOUNT)
                .value("Barcelona")
                .build();
        ciudadTrabajoValue.setClaimSetClaimValue(new ClaimSetClaimValueJpaEntity(trabajoClaimSet, ciudadTrabajoValue));
        ciudadTrabajoValue = claimValueRepo.save(ciudadTrabajoValue);

        // Test: Search for "Trabajo" should return all values from the trabajo ClaimSet
        mockMvc.perform(get("/api/v1/claim-values/related")
                        .param("ownerType", "ACCOUNT")
                        .param("ownerId", testAccount.getId().toString())
                        .param("searchValue", "Trabajo")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(3))
                .andExpect(jsonPath("$[?(@.value == 'Trabajo')]").exists())
                .andExpect(jsonPath("$[?(@.value == 'Av. Principal 456')]").exists())
                .andExpect(jsonPath("$[?(@.value == 'Barcelona')]").exists());

        // Test: Search for "Casa" should return all values from the casa ClaimSet
        mockMvc.perform(get("/api/v1/claim-values/related")
                        .param("ownerType", "ACCOUNT")
                        .param("ownerId", testAccount.getId().toString())
                        .param("searchValue", "Casa")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(3))
                .andExpect(jsonPath("$[?(@.value == 'Casa')]").exists())
                .andExpect(jsonPath("$[?(@.value == 'Calle 123')]").exists())
                .andExpect(jsonPath("$[?(@.value == 'Madrid')]").exists());
    }
}