# VU One Auth Server (Formerly World Server)

The VU One Auth Server is the central authentication and authorization service for the VU One platform. It manages user identities, roles, permissions, and provides OAuth 2.0 and OpenID Connect capabilities for secure access to platform resources.

## Overview

The Auth Server is built with Spring Boot and provides comprehensive identity and access management features:

- **User Authentication**: Secure user login and session management
- **Authorization**: Role-based access control (RBAC) with fine-grained permissions
- **Multi-tenant Support**: Tenant-specific user management and data isolation
- **OAuth 2.0/OpenID Connect**: Standards-based authentication protocols
- **API Security**: API key validation and service-to-service authentication

## Prerequisites

- Java 21 or higher
- Maven 3.6.x or higher
- Database (SQL Server, PostgreSQL, or H2 for development)
- Access to Multitenant Service for tenant configuration

## Dependencies

The Auth Server depends on the following external services:
- **Multitenant Service**: Provides tenant-specific database configurations and encryption keys
- **Database**: Stores user accounts, roles, permissions, and business data

## Environment Variables

The following environment variables are used by the Auth Server:

### Core Application Variables

| Variable | Description | Default Value | Required |
|----------|-------------|---------------|----------|
| `SERVER_PORT` | Server port | `8080` | No |
| `CONTEXT_PATH` | Application context path | `` (empty) | No |
| `API_KEY` | API key for service authentication | - | **Yes** |

### Multitenant Configuration

| Variable | Description | Default Value | Required |
|----------|-------------|---------------|----------|
| `MULTITENANT_KEY` | Key for multitenant encryption | - | **Yes** |
| `MULTITENANT_URL` | URL for the multitenant service | - | **Yes** |
| `MULTITENANT_TRANSIT_KEY` | Transit key for multitenant encryption | - | **Yes** |
| `MULTITENANT_ID_HEADER` | Header for tenant identification | `X-ORIGINAL-HOST` | No |
| `MULTITENANT_JPA_MODE` | JPA mode for multitenant operations | `full` | No |
| `MULTITENANT_SCHEMA_INIT` | Enable schema initialization | `true` | No |

### Database Connection Pool

| Variable | Description | Default Value | Required |
|----------|-------------|---------------|----------|
| `POOL_SIZE` | Database connection pool maximum size | `200` | No |
| `IDLE_TIMEOUT` | Connection idle timeout (ms) | `120000` | No |
| `MIN_IDLE` | Minimum idle connections | `5` | No |
| `CONN_TIMEOUT` | Connection timeout (ms) | `30000` | No |
| `VAL_TIMEOUT` | Validation timeout (ms) | `120000` | No |

### Hibernate Configuration

| Variable | Description | Default Value | Required |
|----------|-------------|---------------|----------|
| `HIB_SHOW_SQL` | Show SQL statements in logs | `false` | No |
| `HIB_HBM2DDL_AUTO` | Hibernate DDL auto mode | `update` | No |
| `HIB_CONN_UNICODE` | Enable Unicode support | `true` | No |
| `HIB_CONN_ENCODING` | Database connection encoding | `UTF-8` | No |
| `HIB_CONN_CHARSET` | Database connection charset | `UTF-8` | No |
| `HIB_DIALECT` | Hibernate SQL dialect | `org.hibernate.dialect.SQLServerDialect` | No |
| `HIB_IMPLICIT_NAMING_STRATEGY` | Hibernate implicit naming strategy | `org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl` | No |
| `HIB_PHYSICAL_NAMING_STRATEGY` | Hibernate physical naming strategy | `org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy` | No |

### SSL Configuration (when using SSL profile)

| Variable | Description | Default Value | Required |
|----------|-------------|---------------|----------|
| `SSL_KEY_STORE` | Path to SSL keystore | `/opt/certs/cert.p12` | SSL Only |
| `SSL_KEY_STORE_PASSWORD` | SSL keystore password | - | SSL Only |
| `SSL_KEY_STORE_TYPE` | SSL keystore type | `PKCS12` | No |
| `SSL_KEY_ALIAS` | SSL key alias | `` (empty) | No |
| `SPRING_SSL_TRUST_STORE` | Path to SSL truststore | - | SSL Only |
| `SPRING_SSL_TRUST_STORE_PASSWORD` | SSL truststore password | - | SSL Only |

### API Context Paths

| Variable | Description | Default Value | Required |
|----------|-------------|---------------|----------|
| `AUTHORIZATION_CONTEXT_PATH` | Authorization API context path | `/api/v1` | No |
| `CLAIM_CONTEXT_PATH` | Claims API context path | `/api/v1` | No |
| `BUSINESS_CONTEXT_PATH` | Business API context path | `/api/v1` | No |

### Data Seeding (Security Critical)

| Variable | Description | Default Value | Required |
|----------|-------------|---------------|----------|
| `ENABLE_DATA_SEEDING` | **SECURITY CRITICAL**: Enable automatic data seeding of system accounts, roles, and claims | `false` | No |

> ⚠️ **Security Warning**: `ENABLE_DATA_SEEDING` should only be set to `true` in development environments. Never enable in production as it creates default system accounts and data.

## Building the Project

```bash
# Build the project
./mvnw clean package

# Skip tests during build
./mvnw clean package -DskipTests

# Run tests only
./mvnw test

# Clean and rebuild
./mvnw clean package
```

## Running the Application

### Using Maven

```bash
# Run with default profile
./mvnw spring-boot:run

# Run with specific profile
./mvnw spring-boot:run -Dspring-boot.run.profiles=development

# Run with SSL profile
./mvnw spring-boot:run -Dspring-boot.run.profiles=ssl
```

### Using JAR

```bash
# After building the project
java -jar target/auth-server-*.jar
```

### Using Docker

```bash
# Build Docker image
docker build -f Docker/Dockerfile -t vu-one-auth-server .

# Run container (basic configuration)
docker run -p 8080:8080 \
  -e API_KEY=your-api-key \
  -e MULTITENANT_KEY=your-multitenant-key \
  -e MULTITENANT_URL=http://onprem:3000 \
  -e MULTITENANT_TRANSIT_KEY=your-transit-key \
  vu-one-auth-server

# Run container with SSL (production-like)
docker run -p 8443:8443 \
  -e API_KEY=your-api-key \
  -e MULTITENANT_KEY=your-multitenant-key \
  -e MULTITENANT_URL=http://onprem:3000 \
  -e MULTITENANT_TRANSIT_KEY=your-transit-key \
  -e SERVER_PORT=8443 \
  -e SSL_KEY_STORE=/opt/certs/cert.p12 \
  -e SSL_KEY_STORE_PASSWORD=your-keystore-password \
  -e SPRING_SSL_TRUST_STORE=/opt/certs/truststore.p12 \
  -e SPRING_SSL_TRUST_STORE_PASSWORD=your-truststore-password \
  -v /path/to/certs:/opt/certs \
  --profile ssl \
  vu-one-auth-server
```

## Database Configuration

The Auth Server requires a database for storing user data, roles, and permissions. Configure the database connection through the multitenant service, which provides tenant-specific database configurations.

For development, you can use an in-memory H2 database, but production deployments should use a persistent database like SQL Server or PostgreSQL.

## API Endpoints

The Auth Server provides several API contexts:

- **Authorization API** (`/api/v1/authorization`): User authentication and authorization
- **Claims API** (`/api/v1/claims`): User claims and token management
- **Business API** (`/api/v1/business`): Business-specific operations
- **Accounts API** (`/api/v1/accounts`): User account management
- **Identities API** (`/api/v1/identities`): Identity management
- **Permissions API** (`/api/v1/permissions`): Permission management
- **Roles API** (`/api/v1/roles`): Role management

## Health Checks

The service provides health checks at `/actuator/health` that monitor:

- Application liveness and readiness
- Database connectivity
- Multitenant Service connectivity

## Security

The Auth Server implements comprehensive security measures:

- **API Key Authentication**: Service-to-service authentication using API keys
- **Multi-tenant Isolation**: Tenant-specific data access and encryption
- **Role-based Access Control**: Fine-grained permission system
- **Secure Token Management**: JWT and OAuth 2.0 token handling

## Configuration

The service uses Spring Boot's externalized configuration with key areas:

- **Server Configuration**: Port and context path settings
- **Database Configuration**: Connection pooling and JPA settings
- **Multitenant Configuration**: Tenant management and encryption
- **Security Configuration**: Authentication and authorization settings
- **API Configuration**: Context paths for different API groups

### Spring Profiles

The application supports several Spring profiles for different environments:

| Profile | Description | Use Case |
|---------|-------------|----------|
| `default` | Standard configuration | Production and general use |
| `development` | Development settings with data seeding enabled | Local development |
| `ssl` | SSL/TLS configuration for HTTPS | Production with SSL certificates |
| `ssldevelopment` | SSL configuration for development | Local development with self-signed certificates |
| `test` | Test configuration | Integration and unit tests |

**Activate profiles using:**
```bash
# Single profile
java -jar auth-server.jar --spring.profiles.active=development

# Multiple profiles
java -jar auth-server.jar --spring.profiles.active=ssl,production

# With Maven
mvn spring-boot:run -Dspring-boot.run.profiles=development
```
