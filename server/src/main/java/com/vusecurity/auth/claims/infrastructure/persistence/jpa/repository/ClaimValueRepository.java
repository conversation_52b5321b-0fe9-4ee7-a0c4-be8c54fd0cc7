package com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.OwnerType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Spring Data JPA repository for ClaimValueJpaEntity.
 * Infrastructure-only interface for database operations.
 */
@Repository
public interface ClaimValueRepository extends JpaRepository<ClaimValueJpaEntity, UUID>, JpaSpecificationExecutor<ClaimValueJpaEntity> {

    /**
     * Find claim value by ID.
     *
     * @param id the claim value ID
     * @return the claim value if found
     */
    Optional<ClaimValueJpaEntity> findById(UUID id);

    /**
     * Find claim values by value pattern.
     *
     * @param valuePattern the value pattern
     * @param pageable     pagination information
     * @return page of matching claim values
     */
    Page<ClaimValueJpaEntity> findByValueContaining(String valuePattern, Pageable pageable);


    boolean existsByOwnerTypeAndOwnerIdAndClaimDefinitionId(OwnerType ownerType,
                                                            UUID ownerId,
                                                            UUID claimDefinitionId);

    @Query("""
           select (count(cv) > 0) from ClaimValueJpaEntity cv
             join AccountJpaEntity acc
               on acc.id = cv.ownerId
           where cv.ownerType           = 'ACCOUNT'
             and acc.accountType        = :accountType
             and cv.claimDefinition.id  = :claimDefinitionId
             and lower(cv.value)        = :valueLower
             and cv.ownerId            <> :excludeAccountId
           """)
    boolean existsForAccountType(@Param("accountType") AccountType accountType,
                                 @Param("claimDefinitionId") UUID   claimDefinitionId,
                                 @Param("valueLower")        String valueLower,
                                 @Param("excludeAccountId")  UUID   excludeAccountId);

    /**
     * Find all claim values for a specific account ID.
     *
     * @param accountId the account ID
     * @return list of claim values for the account
     */
    @Query("""
            SELECT cv
              FROM ClaimValueJpaEntity cv
              JOIN FETCH cv.claimDefinition cd
             WHERE cv.ownerType = com.vusecurity.auth.contracts.enums.OwnerType.ACCOUNT
               AND cv.ownerId = :accountId
             ORDER BY cd.code
            """)
    List<ClaimValueJpaEntity> findByAccountIdWithDefinition(UUID accountId);

    /**
     * Find all claim values for a specific account ID with their verification.
     *
     * @param accountId the account ID
     * @return list of claim values for the account with verification loaded
     */
    @Query("""
            SELECT DISTINCT cv
              FROM ClaimValueJpaEntity cv
              JOIN FETCH cv.claimDefinition cd
              LEFT JOIN FETCH cv.verification v
             WHERE cv.ownerType = com.vusecurity.auth.contracts.enums.OwnerType.ACCOUNT
               AND cv.ownerId = :accountId
             ORDER BY cd.code
            """)
    List<ClaimValueJpaEntity> findByAccountIdWithDefinitionAndVerification(UUID accountId);

    /**
     * Find claim value by ID with verification.
     *
     * @param id the claim value ID
     * @return the claim value with verification if found
     */
    @Query("""
            SELECT cv
              FROM ClaimValueJpaEntity cv
              LEFT JOIN FETCH cv.verification v
             WHERE cv.id = :id
            """)
    Optional<ClaimValueJpaEntity> findByIdWithVerification(UUID id);
    
    /**
     * Find all claim values by OwnerType and OwnerId.
     *
     * @param ownerType the type of the owner to find
     * @param ownerId the owner ID
     * @return a list of claim values or empty if none found.
     */
    List<ClaimValueJpaEntity> findAllByOwnerTypeAndOwnerId(OwnerType ownerType, UUID ownerId);

    /**
     * Find all claim values by Account and claimSet
     *
     * @param ownerType the type of the owner to find
     * @param ownerId the owner ID
     * @param claimSetId the ID of the claimSet.
     * @return a list of claim values or empty if none found.
     */
    List<ClaimValueJpaEntity> findAllByOwnerTypeAndOwnerIdAndClaimSetClaimValue_ClaimSet_Id(OwnerType ownerType, UUID ownerId, UUID claimSetId);
}
