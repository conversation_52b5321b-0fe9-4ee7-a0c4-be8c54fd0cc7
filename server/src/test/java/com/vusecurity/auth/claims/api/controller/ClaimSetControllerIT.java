package com.vusecurity.auth.claims.api.controller;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetRepository;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.shared.test.BaseIntegrationTest;
import com.vusecurity.business.domain.Business;
import com.vusecurity.business.domain.repositories.BusinessRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.web.server.LocalServerPort;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.UUID;
import java.util.List;

import com.vusecurity.auth.contracts.api.v1.dto.claims.claimset.PatchClaimDefinitionsOnClaimSetRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetDefinitionMappingJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetDefinitionMappingRepository;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.assertj.core.api.Assertions.assertThat;

@AutoConfigureWebMvc
class ClaimSetControllerIT extends BaseIntegrationTest {


    @LocalServerPort
    int port;

    @Autowired
    WebApplicationContext webApplicationContext;

    @Autowired
    BusinessRepository businessRepo;

    @Autowired
    ClaimSetRepository claimSetRepo;

    @Autowired
    ClaimDefinitionRepository claimDefinitionRepo;

    @Autowired
    ClaimSetDefinitionMappingRepository mappingRepo;

    @Autowired
    ObjectMapper objectMapper;
    UUID bizA;
    UUID bizB;
    ClaimDefinitionJpaEntity def1;
    ClaimDefinitionJpaEntity def2;
    ClaimDefinitionJpaEntity def3;
    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        // Set up MockMvc
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // 1) Truncate the tables so each test starts clean
        claimSetRepo.deleteAll();
        businessRepo.deleteAll();
        claimDefinitionRepo.deleteAll();

        // 2) Create two Businesses (must satisfy your non-null constraints)
        Business bA = new Business()
                .setName("Business A")
                .setBusinessType(com.vusecurity.business.domain.enums.BusinessTypeEnum.BUSINESS_UNIT);
        bA.setCreatedBy("TEST_USER");

        Business bB = new Business()
                .setName("Business B")
                .setBusinessType(com.vusecurity.business.domain.enums.BusinessTypeEnum.BUSINESS_UNIT);
        bB.setCreatedBy("TEST_USER");

        bA = businessRepo.save(bA);
        bB = businessRepo.save(bB);
        bizA = bA.getId();
        bizB = bB.getId();

        // 3) Create three ClaimSetJpaEntity using the preferred constructor without ID
        ClaimSetJpaEntity cs1 = new ClaimSetJpaEntity(bA, AccountType.WORKFORCE, true);
        cs1.setName("Alpha");
        cs1.setDescription("Alpha description");

        ClaimSetJpaEntity cs2 = new ClaimSetJpaEntity(bA, AccountType.CUSTOMER, false);
        cs2.setName("Beta");
        cs2.setDescription("Beta description");

        ClaimSetJpaEntity cs3 = new ClaimSetJpaEntity(bB, AccountType.WORKFORCE, true);
        cs3.setName("Gamma");
        cs3.setDescription("Gamma description");

        claimSetRepo.save(cs1);
        claimSetRepo.save(cs2);
        claimSetRepo.save(cs3);

        // Add claim definitions
        def1 = new ClaimDefinitionJpaEntity("code1", "ClaimDef1", "desc1", DataTypeEnum.STRING, null);
        def2 = new ClaimDefinitionJpaEntity("code2", "ClaimDef2", "desc2", DataTypeEnum.STRING, null);
        def3 = new ClaimDefinitionJpaEntity("code3", "ClaimDef3", "desc3", DataTypeEnum.STRING, null);
        def1 = claimDefinitionRepo.save(def1);
        def2 = claimDefinitionRepo.save(def2);
        def3 = claimDefinitionRepo.save(def3);
    }

    @Test
    void whenFilterByBusinessA_andPage1_thenReturnsAlphaAndBeta() throws Exception {
        mockMvc.perform(get("/api/v1/claim-sets")
                        .param("page", "1")
                        .param("pageSize", "10")
                        .param("businessId", bizA.toString())
                        .param("sortBy", "name")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.currentPage").value(1))
                .andExpect(jsonPath("$.pageSize").value(10))
                .andExpect(jsonPath("$.totalElements").value(2))
                .andExpect(jsonPath("$.content.length()").value(2))
                .andExpect(jsonPath("$.content[0].name").value("Alpha"))
                .andExpect(jsonPath("$.content[1].name").value("Beta"))
                .andExpect(jsonPath("$.content[0].businessName").value("Business A"))
                .andExpect(jsonPath("$.content[1].businessName").value("Business A"));
    }

    @Test
    void whenPage2_pageSize2_thenReturnsGamma() throws Exception {
        mockMvc.perform(get("/api/v1/claim-sets")
                        .param("page", "2")
                        .param("pageSize", "2")
                        .param("sortBy", "name")
                        .param("sortDirection", "ASC")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.currentPage").value(2))
                .andExpect(jsonPath("$.pageSize").value(2))
                .andExpect(jsonPath("$.totalElements").value(3))
                .andExpect(jsonPath("$.content.length()").value(1))
                .andExpect(jsonPath("$.content[0].name").value("Gamma"))
                .andExpect(jsonPath("$.content[0].businessName").value("Business B"));
    }

    @Test
    void whenSortByNameAsc_thenReturnsAlphabeticalOrder() throws Exception {
        mockMvc.perform(get("/api/v1/claim-sets")
                        .param("page", "1")
                        .param("pageSize", "10")
                        .param("sortBy", "name")
                        .param("sortDirection", "ASC")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.currentPage").value(1))
                .andExpect(jsonPath("$.pageSize").value(10))
                .andExpect(jsonPath("$.totalElements").value(3))
                .andExpect(jsonPath("$.content.length()").value(3))
                .andExpect(jsonPath("$.content[0].name").value("Alpha"))
                .andExpect(jsonPath("$.content[1].name").value("Beta"))
                .andExpect(jsonPath("$.content[2].name").value("Gamma"))
                .andExpect(jsonPath("$.content[0].businessName").value("Business A"))
                .andExpect(jsonPath("$.content[1].businessName").value("Business A"))
                .andExpect(jsonPath("$.content[2].businessName").value("Business B"));
    }

    @Test
    void whenSortByNameDesc_thenReturnsReverseAlphabeticalOrder() throws Exception {
        mockMvc.perform(get("/api/v1/claim-sets")
                        .param("page", "1")
                        .param("pageSize", "10")
                        .param("sortBy", "name")
                        .param("sortDirection", "DESC")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.currentPage").value(1))
                .andExpect(jsonPath("$.pageSize").value(10))
                .andExpect(jsonPath("$.totalElements").value(3))
                .andExpect(jsonPath("$.content.length()").value(3))
                .andExpect(jsonPath("$.content[0].name").value("Gamma"))
                .andExpect(jsonPath("$.content[1].name").value("Beta"))
                .andExpect(jsonPath("$.content[2].name").value("Alpha"))
                .andExpect(jsonPath("$.content[0].businessName").value("Business B"))
                .andExpect(jsonPath("$.content[1].businessName").value("Business A"))
                .andExpect(jsonPath("$.content[2].businessName").value("Business A"));
    }

    @Test
    void whenNoSortingParameters_thenReturnsDefaultOrdering() throws Exception {
        mockMvc.perform(get("/api/v1/claim-sets")
                        .param("page", "1")
                        .param("pageSize", "10")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.currentPage").value(1))
                .andExpect(jsonPath("$.pageSize").value(10))
                .andExpect(jsonPath("$.totalElements").value(3))
                .andExpect(jsonPath("$.content.length()").value(3))
                .andExpect(jsonPath("$.content[0].businessName").exists());
    }

    @Test
    void patchClaimDefinitions_addsNewClaimDefinition() throws Exception {
        ClaimSetJpaEntity cs = claimSetRepo.findAll().getFirst();


        PatchClaimDefinitionsOnClaimSetRequest.ClaimDefinitionsToClaimSetRequest claimDef = new PatchClaimDefinitionsOnClaimSetRequest.ClaimDefinitionsToClaimSetRequest();
        claimDef.setClaimDefinitionId(def1.getId());
        claimDef.setClaimDefinitionOrder(1);
        claimDef.setEnforceUniqueness(true);

        PatchClaimDefinitionsOnClaimSetRequest request = new PatchClaimDefinitionsOnClaimSetRequest();
        request.setClaimDefinitions(List.of(claimDef));

        mockMvc.perform(MockMvcRequestBuilders.patch("/api/v1/claim-sets/" + cs.getId() + "/claim-definitions")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.claimSetId").value(cs.getId().toString()))
                .andExpect(jsonPath("$.claimDefinitions[0].claimDefinitionId").value(def1.getId().toString()))
                .andExpect(jsonPath("$.claimDefinitions[0].claimDefinitionOrder").value(1))
                .andExpect(jsonPath("$.claimDefinitions[0].enforceUniqueness").value(true));
        // DB assertion
        var mappings = mappingRepo.findByClaimSetId(cs.getId());
        assertThat(mappings).anyMatch(m -> m.getClaimDefinitionId().equals(def1.getId()) && Boolean.TRUE.equals(m.getEnforceUniqueness()));
    }

    @Test
    void patchClaimDefinitions_updatesExistingMapping() throws Exception {
        ClaimSetJpaEntity cs = claimSetRepo.findAll().getFirst();

        ClaimSetDefinitionMappingJpaEntity mapping = new ClaimSetDefinitionMappingJpaEntity(cs, def2);
        mapping.setClaimDefinitionOrder(1);
        mapping.setEnforceUniqueness(false);
        mappingRepo.save(mapping);

        // Patch to update order and enforceUniqueness
        PatchClaimDefinitionsOnClaimSetRequest.ClaimDefinitionsToClaimSetRequest claimDef = new PatchClaimDefinitionsOnClaimSetRequest.ClaimDefinitionsToClaimSetRequest();
        claimDef.setClaimDefinitionId(def2.getId());
        claimDef.setClaimDefinitionOrder(5);
        claimDef.setEnforceUniqueness(true);

        PatchClaimDefinitionsOnClaimSetRequest request = new PatchClaimDefinitionsOnClaimSetRequest();
        request.setClaimDefinitions(List.of(claimDef));

        mockMvc.perform(MockMvcRequestBuilders.patch("/api/v1/claim-sets/" + cs.getId() + "/claim-definitions")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.claimDefinitions[0].claimDefinitionOrder").value(5))
                .andExpect(jsonPath("$.claimDefinitions[0].enforceUniqueness").value(true));
        // DB assertion
        var updated = mappingRepo.findByClaimSetIdAndClaimDefinitionId(cs.getId(), def2.getId());
        assertThat(updated.getClaimDefinitionOrder()).isEqualTo(5);
        assertThat(updated.getEnforceUniqueness()).isTrue();
    }

    @Test
    void patchClaimDefinitions_duplicateClaimDefinitionIds_lastWins() throws Exception {
        ClaimSetJpaEntity cs = claimSetRepo.findAll().getFirst();

        // Test multiple duplicates to ensure last occurrence wins
        PatchClaimDefinitionsOnClaimSetRequest.ClaimDefinitionsToClaimSetRequest claimDef1 = new PatchClaimDefinitionsOnClaimSetRequest.ClaimDefinitionsToClaimSetRequest();
        claimDef1.setClaimDefinitionId(def3.getId());
        claimDef1.setClaimDefinitionOrder(10);
        claimDef1.setEnforceUniqueness(false);

        PatchClaimDefinitionsOnClaimSetRequest.ClaimDefinitionsToClaimSetRequest claimDef2 = new PatchClaimDefinitionsOnClaimSetRequest.ClaimDefinitionsToClaimSetRequest();
        claimDef2.setClaimDefinitionId(def3.getId());
        claimDef2.setClaimDefinitionOrder(20);
        claimDef2.setEnforceUniqueness(true);

        PatchClaimDefinitionsOnClaimSetRequest.ClaimDefinitionsToClaimSetRequest claimDef3 = new PatchClaimDefinitionsOnClaimSetRequest.ClaimDefinitionsToClaimSetRequest();
        claimDef3.setClaimDefinitionId(def3.getId());
        claimDef3.setClaimDefinitionOrder(30);
        claimDef3.setEnforceUniqueness(false);

        PatchClaimDefinitionsOnClaimSetRequest request = new PatchClaimDefinitionsOnClaimSetRequest();
        request.setClaimDefinitions(List.of(claimDef1, claimDef2, claimDef3));

        mockMvc.perform(MockMvcRequestBuilders.patch("/api/v1/claim-sets/" + cs.getId() + "/claim-definitions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk());

        // DB assertion - should have the values from the last occurrence (claimDef3)
        var mapping = mappingRepo.findByClaimSetIdAndClaimDefinitionId(cs.getId(), def3.getId());
        assertThat(mapping.getClaimDefinitionOrder()).isEqualTo(30);
        assertThat(mapping.getEnforceUniqueness()).isFalse();
        
        // Verify only one mapping exists for this claimDefinitionId (no duplicates in DB)
        var allMappings = mappingRepo.findByClaimSetId(cs.getId());
        long countForDef3 = allMappings.stream()
                .filter(m -> m.getClaimDefinitionId().equals(def3.getId()))
                .count();
        assertThat(countForDef3).isEqualTo(1);
    }

    @Test
    void patchClaimDefinitions_multipleDuplicates_lastOccurrenceWins() throws Exception {
        ClaimSetJpaEntity cs = claimSetRepo.findAll().getFirst();

        // Create multiple assignments with the same claimDefinitionId but different values
        PatchClaimDefinitionsOnClaimSetRequest.ClaimDefinitionsToClaimSetRequest claimDef1 = new PatchClaimDefinitionsOnClaimSetRequest.ClaimDefinitionsToClaimSetRequest();
        claimDef1.setClaimDefinitionId(def1.getId());
        claimDef1.setClaimDefinitionOrder(10);
        claimDef1.setEnforceUniqueness(false);

        PatchClaimDefinitionsOnClaimSetRequest.ClaimDefinitionsToClaimSetRequest claimDef2 = new PatchClaimDefinitionsOnClaimSetRequest.ClaimDefinitionsToClaimSetRequest();
        claimDef2.setClaimDefinitionId(def1.getId());
        claimDef2.setClaimDefinitionOrder(20);
        claimDef2.setEnforceUniqueness(true);

        PatchClaimDefinitionsOnClaimSetRequest.ClaimDefinitionsToClaimSetRequest claimDef3 = new PatchClaimDefinitionsOnClaimSetRequest.ClaimDefinitionsToClaimSetRequest();
        claimDef3.setClaimDefinitionId(def1.getId());
        claimDef3.setClaimDefinitionOrder(30);
        claimDef3.setEnforceUniqueness(false);

        PatchClaimDefinitionsOnClaimSetRequest request = new PatchClaimDefinitionsOnClaimSetRequest();
        request.setClaimDefinitions(List.of(claimDef1, claimDef2, claimDef3));

        mockMvc.perform(MockMvcRequestBuilders.patch("/api/v1/claim-sets/" + cs.getId() + "/claim-definitions")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk());

        // DB assertion - should have the values from the last occurrence (claimDef3)
        var mapping = mappingRepo.findByClaimSetIdAndClaimDefinitionId(cs.getId(), def1.getId());
        assertThat(mapping.getClaimDefinitionOrder()).isEqualTo(30);
        assertThat(mapping.getEnforceUniqueness()).isFalse();
    }

    @Test
    void patchClaimDefinitions_invalidClaimDefinitionId_returnsError() throws Exception {
        ClaimSetJpaEntity cs = claimSetRepo.findAll().getFirst();

        UUID invalidId = UUID.randomUUID();

        PatchClaimDefinitionsOnClaimSetRequest.ClaimDefinitionsToClaimSetRequest claimDef = new PatchClaimDefinitionsOnClaimSetRequest.ClaimDefinitionsToClaimSetRequest();
        claimDef.setClaimDefinitionId(invalidId);
        claimDef.setClaimDefinitionOrder(1);
        claimDef.setEnforceUniqueness(true);

        PatchClaimDefinitionsOnClaimSetRequest request = new PatchClaimDefinitionsOnClaimSetRequest();
        request.setClaimDefinitions(List.of(claimDef));

        mockMvc.perform(MockMvcRequestBuilders.patch("/api/v1/claim-sets/" + cs.getId() + "/claim-definitions")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().is4xxClientError());
        // DB assertion: no mapping created
        var mappings = mappingRepo.findByClaimSetId(cs.getId());
        assertThat(mappings).noneMatch(m -> m.getClaimDefinitionId().equals(invalidId));
    }
}