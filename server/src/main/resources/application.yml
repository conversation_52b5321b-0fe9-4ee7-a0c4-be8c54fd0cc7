# Server configuration
server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: ${CONTEXT_PATH:}

swagger:
  oauth:
    token-url-base: ${SERVER_SWAGGER_URL:http://localhost:8082}

spring:
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration, org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration
  application:
    name: @name@
  jackson:
    property-naming-strategy: SNAKE_CASE

management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always
      show-components: always
      group:
        liveness:
          include: ping
        readiness:
          include: "*"
    trace:
      enabled: true
  health:
    diskspace:
      enabled: false
    livenessstate:
      enabled: true
    readinessstate:
      enabled: true
    db:
      enabled: false

app:
  apikey: ${API_KEY}
  multitenant:
    key: ${MULTITENANT_KEY}
    url: ${MULTITENANT_URL}
    transitKey: ${MULTITENANT_TRANSIT_KEY}
    idHeader: ${MULTITENANT_ID_HEADER:X-ORIGINAL-HOST}
    applicationName: @project.name@
    jpa:
      hikari:
        maximum-pool-size: ${POOL_SIZE:200}
        idle-timeout: ${IDLE_TIMEOUT:120000}
        minimum-idle: ${MIN_IDLE:5}
        connection-timeout: ${CONN_TIMEOUT:30000}
        validation-timeout: ${VAL_TIMEOUT:120000}
      mode: ${MULTITENANT_JPA_MODE:full}
  version: @app.version@
  build: @build.version@
  date: @build.date@
  # Data seeding configuration - SECURITY CRITICAL
  # Controls whether initial data (system accounts, roles, claims) is automatically seeded
  dataseed:
    # Enable/disable data seeding (default: false for security)
    # Can be overridden by ENABLE_DATA_SEEDING environment variable
    enabled: ${ENABLE_DATA_SEEDING:false}
  authorization:
    context-path: ${AUTHORIZATION_CONTEXT_PATH:/api/v1}
    autoconfigure:
      service: true
  claims:
    context-path: ${CLAIM_CONTEXT_PATH:/api/v1}
    autoconfigure:
      service: true
  business:
    context-path: ${BUSINESS_CONTEXT_PATH:/api/v1}
    autoconfigure:
      services: true
  factors-server:
    url: ${FACTOR_SERVER_URL:http://localhost:8083}
    apikey: ${FACTOR_SERVER_APIKEY}
  # OIDC UserInfo endpoint configuration
  oidc:
    userinfo:
      # Custom claim mappings from internal claim codes to OIDC standard claim names
      claim-mappings:
        email_address: email
        phone_number: phone_number
        first_name: given_name
        last_name: family_name
        birth_date: birthdate
        profile_url: profile
        picture_url: picture
        website_url: website
      # Claims to exclude from UserInfo response (sensitive or internal-only)
      excluded-claims:
        - password_hash
        - password_salt
        - totp_secret
        - recovery_codes
        - api_keys
        - internal_id
        - password
        - salt
      # Metadata keys to include in UserInfo response (empty means use default filtering)
      included-metadata-keys: []
      # Whether to include custom VU Security claims (business_id, identity_id, etc.)
      include-custom-claims: true
      # Whether to include account metadata in UserInfo response
      include-metadata: true
      # Whether to include address claim as structured object
      include-address: true
      # Fallback display name format when first_name and last_name are not available
      fallback-display-name-format: "{identity_name}"
      # Default values for standard OIDC claims when not available from account claims
      default-claim-values:
        email_verified: false
        phone_number_verified: false
logging:
  level:
    root: info

hibernate:
  show_sql: ${HIB_SHOW_SQL:false}
  hbm2ddl.auto: ${HIB_HBM2DDL:update}
  connection.useUnicode: ${HIB_CONN_UNICODE:true}
  connection.characterEncoding: ${HIB_CONN_ENCODING:UTF-8}
  connection.charSet: ${HIB_CONN_CHARSET:UTF-8}
  dialect: ${HIB_DIALECT:org.hibernate.dialect.SQLServerDialect}
  implicit_naming_strategy: ${HIB_IMPLICIT_NAMING_STRATEGY:org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl}
  physical_naming_strategy: ${HIB_PHYSICAL_NAMING_STRATEGY:org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy}