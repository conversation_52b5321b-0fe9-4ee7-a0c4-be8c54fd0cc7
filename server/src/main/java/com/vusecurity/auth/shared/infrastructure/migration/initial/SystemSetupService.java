package com.vusecurity.auth.shared.infrastructure.migration.initial;

import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.IdentityType;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityProviderJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.IdentityProviderRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.IdentityRepository;

import com.vusecurity.core.commons.Auditable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import com.vusecurity.business.domain.Business;
import com.vusecurity.business.domain.enums.BusinessTypeEnum;
import com.vusecurity.business.domain.repositories.BusinessRepository;
import com.vusecurity.channel.commons.api.model.enums.ChannelType;
import com.vusecurity.channel.domain.Channel;
import com.vusecurity.channel.domain.repositories.ChannelRepository;
import com.vusecurity.core.commons.Auditable.Status;

import lombok.AllArgsConstructor;

@Service
@AllArgsConstructor
public class SystemSetupService {

    private static final Logger logger = LoggerFactory.getLogger(SystemSetupService.class);
    public static final String SYSTEM = "SYSTEM";

    private final AccountRepository accountRepository;
    private final IdentityRepository identityRepository;
    private final BusinessRepository businessRepository;
    private final IdentityProviderRepository identityProviderRepository;
    private final ChannelRepository channelRepository;


    public void start() {
        logger.info("SystemSetupService: Starting system data initialization...");
        logger.trace("Entering start method.");

        // Create identity provider first if it doesn't exist
        IdentityProviderJpaEntity identityProvider;
        long identityProviderCount = identityProviderRepository.count();
        logger.debug("Current identity provider count: {}", identityProviderCount);

        if (identityProviderCount == 0) {
            logger.info("No identity providers found in the repository. Starting identity provider setup.");

            identityProvider = new IdentityProviderJpaEntity(DataSeedConstants.IDENTITY_PROVIDER_ID, "LOCAL");
            identityProvider.activate();

            identityProviderRepository.save(identityProvider);
            logger.info("Identity provider created and saved: {}", identityProvider);
        } else if (identityProviderCount == 1) {
            logger.info("Only one identity provider found in the repository. Starting SCIM identity provider setup.");
            IdentityProviderJpaEntity scimIdentityProvider = new IdentityProviderJpaEntity(DataSeedConstants.SCIM_PROVIDER_ID, "SCIM");
            scimIdentityProvider.activate();

            identityProviderRepository.save(scimIdentityProvider);
            logger.info("SCIM Identity provider created and saved: {}", scimIdentityProvider);

             identityProvider = identityProviderRepository.findAll(org.springframework.data.domain.PageRequest.of(0, 1, Sort.by("createdAt")))
                    .getContent().getFirst();
        } else {
            logger.info("Identity providers already exist (count: {}). Using existing provider.", identityProviderCount);
            // Get the first identity provider for the accounts created below
            identityProvider = identityProviderRepository.findAll(org.springframework.data.domain.PageRequest.of(0, 1))
                    .getContent().getFirst();
            logger.debug("Using existing identity provider: {}", identityProvider.getId());
        }

        long businessCount = businessRepository.count();
        logger.debug("Current business count: {}", businessCount);

        if (businessCount == 0) {
            logger.info("No businesses found in the repository. Starting system business setup.");

            Business business = new Business();
            business.setId(DataSeedConstants.SYSTEM_BUSINESS_ID)
                    .setName("Default")
                    .setDescription("Default system business")
                    .setBusinessType(BusinessTypeEnum.BUSINESS_UNIT)
                    .setCreatedBy(SYSTEM);
            businessRepository.save(business);
            logger.info("System business created and saved with ID: {}", business.getId());

            // Create system identity using builder pattern
            IdentityJpaEntity systemIdentity = new IdentityJpaEntity(
                    DataSeedConstants.SYSTEM_IDENTITY_ID,
                    IdentityType.SERVICE,
                    SYSTEM
            );
            systemIdentity.activate();
            identityRepository.save(systemIdentity);
            logger.info("System identity created and saved with ID: {}", systemIdentity.getId());

            AccountJpaEntity systemAccount = new AccountJpaEntity(
                    DataSeedConstants.SYSTEM_ACCOUNT_ID,
                    business,
                    systemIdentity,
                    identityProvider,
                    AccountType.SERVICE
            );
            systemAccount.activate();

            accountRepository.save(systemAccount);
            logger.info("System account created and saved with ID: {}", systemAccount.getId());

            logger.info("SystemSetupService: System data initialization completed successfully");
        } else {
            logger.info("Businesses already exist in the repository (count: {}). Skipping system business setup.", businessCount);
        }


        if (!channelRepository.findById(DataSeedConstants.SYSTEM_CHANNEL_ID).isPresent()) {
            logger.info("No channels found in the repository. Starting system channel setup.");
            Channel channel = new Channel();
            channel.setCreatedBy("SYSTEM");
            channel.setStatus(Auditable.Status.ACTIVE);
            channel.setChannelStatus(Auditable.Status.ACTIVE);
            channel.setId(DataSeedConstants.SYSTEM_CHANNEL_ID);
            channel.setName("Default");
            channel.setDescription("Default system channel");
            channel.setChannelType(ChannelType.WEB);
            channel.setCreatedBy(SYSTEM);
            channel.setStatus(Status.ACTIVE);
            channel.setChannelStatus(Status.ACTIVE);
            channelRepository.save(channel);
            logger.info("System channel created and saved with ID: {}", channel.getId());
        }
        
        logger.trace("Exiting start method.");
    }
}
