package com.vusecurity.auth.scim.config;

import static org.apache.directory.scim.spec.schema.ServiceProviderConfiguration.AuthenticationSchema.oauth2;
import org.apache.directory.scim.server.configuration.ServerConfiguration;
import org.apache.directory.scim.spring.ScimpleSpringConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import jakarta.ws.rs.core.Application;

@Configuration
@AutoConfigureBefore(ScimpleSpringConfiguration.class)
public class ScimConfig {
    
    @Bean
    Application scimApplication() {
        return new ScimServerApplication();
    }

    @Bean
    ServerConfiguration serverConfiguration() {
        // Set any unique configuration bits
        return new NoPatchServerConfiguration()
            .setId("vu-one")
            .setSupportsChangePassword(false)
            .setDocumentationUri("https://vusecurity.com")
            // set the auth scheme
            .addAuthenticationSchema(oauth2());
    }

    class NoPatchServerConfiguration extends ServerConfiguration {
        
        @Override
        public boolean isSupportsPatch() {
            return false;
        }
    }
}
