package com.vusecurity.auth.claims.api.controller;

import com.vusecurity.auth.claims.application.command.*;
import com.vusecurity.auth.claims.application.handler.*;
import com.vusecurity.auth.claims.application.query.GetClaimSetQuery;
import com.vusecurity.auth.claims.application.query.GetClaimSetsPagedQuery;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.mapper.ClaimSetDtoMapper;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimset.*;
import com.vusecurity.core.commons.models.PageableResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@SecurityRequirement(name = "oauth2 client credentials")
@Tag(name = "Claim Sets", description = "Claim set management operations")
public class ClaimSetController {

    private final CreateClaimSetHandler createClaimSetHandler;
    private final UpdateClaimSetHandler updateClaimSetHandler;
    private final DeleteClaimSetHandler deleteClaimSetHandler;
    private final AddClaimDefinitionsToClaimSetHandler addClaimDefinitionsToClaimSetHandler;
    private final RemoveClaimDefinitionsFromClaimSetHandler removeClaimDefinitionsFromClaimSetHandler;
    private final ReplaceClaimDefinitionsInClaimSetHandler replaceClaimDefinitionsInClaimSetHandler;
    private final PatchClaimDefinitionsOnClaimSetHandler patchClaimDefinitionsOnClaimSetHandler;
    private final GetClaimSetQuery getClaimSetQuery;
    private final ClaimSetDtoMapper claimSetDtoMapper;

    public ClaimSetController(CreateClaimSetHandler createClaimSetHandler,
                             UpdateClaimSetHandler updateClaimSetHandler,
                             DeleteClaimSetHandler deleteClaimSetHandler,
                             AddClaimDefinitionsToClaimSetHandler addClaimDefinitionsToClaimSetHandler,
                             RemoveClaimDefinitionsFromClaimSetHandler removeClaimDefinitionsFromClaimSetHandler,
                             ReplaceClaimDefinitionsInClaimSetHandler replaceClaimDefinitionsInClaimSetHandler,
                             PatchClaimDefinitionsOnClaimSetHandler patchClaimDefinitionsOnClaimSetHandler,
                             GetClaimSetQuery getClaimSetQuery,
                             ClaimSetDtoMapper claimSetDtoMapper) {
        this.createClaimSetHandler = createClaimSetHandler;
        this.updateClaimSetHandler = updateClaimSetHandler;
        this.deleteClaimSetHandler = deleteClaimSetHandler;
        this.addClaimDefinitionsToClaimSetHandler = addClaimDefinitionsToClaimSetHandler;
        this.removeClaimDefinitionsFromClaimSetHandler = removeClaimDefinitionsFromClaimSetHandler;
        this.replaceClaimDefinitionsInClaimSetHandler = replaceClaimDefinitionsInClaimSetHandler;
        this.patchClaimDefinitionsOnClaimSetHandler = patchClaimDefinitionsOnClaimSetHandler;
        this.getClaimSetQuery = getClaimSetQuery;
        this.claimSetDtoMapper = claimSetDtoMapper;
    }

    @Operation(
            summary = "Get all claim sets",
            description = "Retrieves a paginated list of claim sets with optional filtering and sorting. " +
                         "Supports filtering by businessId, hasClaimDefinitions, and hasBusiness parameters. " +
                         "Use sortBy to specify the field to sort by (e.g., 'name', 'createdAt') and sortDirection for 'ASC' or 'DESC'."
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Successful retrieval",
                    content = @Content(schema = @Schema(implementation = PageableResponse.class))),
            @ApiResponse(responseCode = "400", description = "Invalid request parameters"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("${app.claims.context-path}/claim-sets")
    public PageableResponse<ClaimSetResponse> get(@RequestParam(name = "page", defaultValue = "1") int page,
                                                  @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                                                  @RequestParam(name = "filter", required = false) String filter,
                                                  @RequestParam(name = "businessId", required = false) UUID businessId,
                                                  @RequestParam(name = "accountType", required = false) String accountType,
                                                  @RequestParam(name = "isIdentifier", required = false) Boolean isIdentifier,
                                                  @RequestParam(name = "hasClaimDefinitions", required = false) Boolean hasClaimDefinitions,
                                                  @RequestParam(name = "hasBusiness", required = false) Boolean hasBusiness,
                                                  @RequestParam(name = "sortBy", required = false) String sortBy,
                                                  @RequestParam(name = "sortDirection", required = false, defaultValue = "ASC") String sortDirection) {

        GetClaimSetsPagedQuery query = GetClaimSetsPagedQuery.builder()
                .page(page)
                .pageSize(pageSize)
                .filter(filter)
                .businessId(businessId)
                .accountType(accountType)
                .isIdentifier(isIdentifier)
                .hasClaimDefinitions(hasClaimDefinitions)
                .hasBusiness(hasBusiness)
                .sortBy(sortBy)
                .sortDirection(sortDirection)
                .build();

        Page<ClaimSetJpaEntity> claimSets = getClaimSetQuery.getAllClaimSets(query);
        return new PageableResponse<>(claimSets.getNumber() + 1, claimSets.getSize(), claimSets.getTotalElements(),
                claimSets.get().map(claimSetDtoMapper::toResponse).toList());
    }

    @Operation(
            summary = "Create a new claim set",
            description = "Creates a new claim set with the specified name, description, business association, and account type. The name must be unique within the business."
    )
    @ApiResponses({
            @ApiResponse(responseCode = "201", description = "Claim set created successfully",
                    content = @Content(schema = @Schema(implementation = ClaimSetResponse.class))),
            @ApiResponse(responseCode = "400", description = "Invalid request data or validation errors"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Access denied"),
            @ApiResponse(responseCode = "409", description = "Claim set with same name already exists")
    })
    @PostMapping("${app.claims.context-path}/claim-sets")
    @ResponseStatus(HttpStatus.CREATED)
    public ClaimSetResponse create(@RequestBody @Valid CreateClaimSetRequest request) {

        CreateClaimSetCommand cmd = new CreateClaimSetCommand(
                request.getBusinessId(),
                request.getAccountType(),
                request.getIsIdentifier(),
                request.getName(),
                request.getDescription(),
                request.getLookupStrategy());

        ClaimSetJpaEntity entity = createClaimSetHandler.createClaimSet(cmd);
        return claimSetDtoMapper.toResponse(entity);
    }


    @Operation(
            summary = "Get claim set by ID",
            description = "Retrieves a specific claim set by its unique identifier, including all associated claim definitions."
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Claim set found",
                    content = @Content(schema = @Schema(implementation = ClaimSetResponse.class))),
            @ApiResponse(responseCode = "404", description = "Claim set not found"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("${app.claims.context-path}/claim-sets/{id}")
    public ClaimSetResponse get(@PathVariable(value = "id") UUID id) {

        ClaimSetJpaEntity claimSet = getClaimSetQuery.getClaimSetById(id);
        return claimSetDtoMapper.toResponse(claimSet);
    }

    @Operation(summary = "Delete a claimSet", description = "Deletes a ClaimSet by its ID.")
    @ApiResponses({
            @ApiResponse(responseCode = "204", description = "ClaimSet deleted successfully"),
            @ApiResponse(responseCode = "404", description = "ClaimSet not found")
    })
    @DeleteMapping(path = "${app.claims.context-path}/claim-sets/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void delete(@PathVariable(value = "id") UUID id) {
        DeleteClaimSetCommand command = new DeleteClaimSetCommand(id);
        deleteClaimSetHandler.deleteClaimSet(command);
    }

    @Operation(summary = "Update a claimSet", description = "Updates a claimSet's name and description.")
    @ApiResponses({
            @ApiResponse(responseCode = "204", description = "ClaimSet updated successfully"),
            @ApiResponse(responseCode = "404", description = "ClaimSet not found")
    })
    @PatchMapping(path = "${app.claims.context-path}/claim-sets/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void update(@PathVariable(value = "id") UUID id, @RequestBody @Valid UpdateClaimSetRequest request) {
        UpdateClaimSetCommand command = new UpdateClaimSetCommand(
                id,
                request.getBusinessId(),
                request.getAccountType(),
                request.getIsIdentifier(),
                request.getName(),
                request.getDescription(),
                request.getLookupStrategy()
        );
        updateClaimSetHandler.handle(command);
    }

    @Operation(
        summary = "Add claim definitions to a claim set",
        description = "Appends the provided claim definitions to the existing ones of the claim set."
    )
    @ApiResponses({
            @ApiResponse(responseCode = "201", description = "Claim definitions added successfully", content = @Content(schema = @Schema(implementation = ClaimSetClaimDefinitionsResponse.class)))
    })
    @PostMapping("${app.claims.context-path}/claim-sets/{claimSetId}/claim-definitions")
    @ResponseStatus(HttpStatus.CREATED)
    public ClaimSetClaimDefinitionsResponse addClaimDefinitions(
            @PathVariable UUID claimSetId,
            @RequestBody @Valid AddClaimDefinitionsToClaimSetRequest request) {

        AddClaimDefinitionsToClaimSetCommand command = ClaimSetDtoMapper.toClaimDefinitionsToClaimSetCommand(claimSetId, request);
        addClaimDefinitionsToClaimSetHandler.addClaimDefinitionsToClaimSet(command);
        return ClaimSetDtoMapper.toClaimDefinitionsToClaimSetCommandResponse(claimSetId, request.getClaimDefinitions());
    }

    @Operation(summary = "Remove claim definitions from a claim set", description = "Removes a specific claim definition from a claim set.")
    @ApiResponses({
            @ApiResponse(responseCode = "204", description = "Claim definition removed from claim set successfully"),
            @ApiResponse(responseCode = "404", description = "Claim set or claim definition not found")
    })
    @DeleteMapping("${app.claims.context-path}/claim-sets/{claimSetId}/claim-definitions")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void removeClaimDefinitionFromClaimSet(
            @PathVariable UUID claimSetId,
            @RequestBody @Valid RemoveClaimDefinitionsFromClaimSetRequest request) {

        RemoveClaimDefinitionsFromClaimSetCommand command = new RemoveClaimDefinitionsFromClaimSetCommand(
                claimSetId,
                request.getClaimDefinitionIds()
        );

        removeClaimDefinitionsFromClaimSetHandler.removeClaimDefinitionFromClaimSet(command);
    }

    @Operation(
        summary = "Replace claim definitions in a claim set",
        description = "Replaces all existing claim definitions in the claim set with the provided ones. This operation removes all current claim definitions and adds the new ones."
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Claim definitions replaced successfully", content = @Content(schema = @Schema(implementation = ClaimSetClaimDefinitionsResponse.class))),
            @ApiResponse(responseCode = "404", description = "Claim set not found"),
            @ApiResponse(responseCode = "400", description = "Invalid request data or one or more claim definitions not found")
    })
    @PutMapping("${app.claims.context-path}/claim-sets/{claimSetId}/claim-definitions")
    @ResponseStatus(HttpStatus.OK)
    public ClaimSetClaimDefinitionsResponse replaceClaimDefinitions(
            @PathVariable UUID claimSetId,
            @RequestBody @Valid ReplaceClaimDefinitionsOnClaimSetRequest request) {

        ReplaceClaimDefinitionsInClaimSetCommand command = ClaimSetDtoMapper.toReplaceClaimDefinitionsToClaimSetCommand(claimSetId, request);
        replaceClaimDefinitionsInClaimSetHandler.replaceClaimDefinitionsInClaimSet(command);
        return ClaimSetDtoMapper.toReplaceClaimDefinitionsToClaimSetCommandResponse(claimSetId, request.getClaimDefinitions());
    }

    @Operation(
        summary = "Partially update claim definitions in a claim set",
        description = "Partially updates claim definitions in a claim set. This operation allows adding new claim definitions or updating properties (order and enforceUniqueness) of existing ones without affecting claim definitions not mentioned in the request."
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Claim definitions patched successfully", content = @Content(schema = @Schema(implementation = ClaimSetClaimDefinitionsResponse.class))),
            @ApiResponse(responseCode = "404", description = "Claim set not found"),
            @ApiResponse(responseCode = "400", description = "Invalid request data or one or more claim definitions not found")
    })
    @PatchMapping("${app.claims.context-path}/claim-sets/{claimSetId}/claim-definitions")
    @ResponseStatus(HttpStatus.OK)
    public ClaimSetClaimDefinitionsResponse patchClaimDefinitions(
            @PathVariable UUID claimSetId,
            @RequestBody @Valid PatchClaimDefinitionsOnClaimSetRequest request) {

        PatchClaimDefinitionsOnClaimSetCommand command = ClaimSetDtoMapper.toPatchClaimDefinitionsToClaimSetCommand(claimSetId, request);
        patchClaimDefinitionsOnClaimSetHandler.patchClaimDefinitionsOnClaimSet(command);
        return ClaimSetDtoMapper.toPatchClaimDefinitionsToClaimSetCommandResponse(claimSetId, request.getClaimDefinitions());
    }

}
