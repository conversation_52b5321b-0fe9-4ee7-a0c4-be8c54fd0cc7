package com.vusecurity.auth.contracts.enums;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.format.DateTimeParseException;
import java.util.UUID;

public enum DataTypeEnum {
    STRING {
        @Override
        public boolean isValid(String value) {
            if (value == null) {
                throw new NullPointerException("String value cannot be null");
            }
            return true;
        }

        @Override
        public String normalize(String value) {
            if (value == null) {
                throw new NullPointerException("String value cannot be null");
            }
            return value.trim();
        }
    },
    NUMERIC {
        @Override
        public boolean isValid(String value) {
            return value.matches("^[+-]?(\\d*[.])?\\d+([eE][+-]?\\d+)?$");
        }

        @Override
        public String normalize(String value) {
            try {
                BigDecimal decimal = new BigDecimal(value).stripTrailingZeros();
                // Use scientific notation for very large or very small numbers
                String plainString = decimal.toPlainString();
                if (plainString.length() > 10 || (plainString.contains(".") && plainString.indexOf('.') > 7)) {
                    return decimal.toString();
                }
                return plainString;
            } catch (NumberFormatException e) {
                // Return original value if parsing fails
                return value;
            }
        }
    },
    BOOL {
        @Override
        public boolean isValid(String value) {
            return value.equalsIgnoreCase("true") || value.equalsIgnoreCase("false")
                    || value.equals("1") || value.equals("0");
        }

        @Override
        public String normalize(String value) {
            return (value.equalsIgnoreCase("true") || value.equals("1")) ? "true" : "false";
        }
    },
    IMAGE {
        @Override
        public boolean isValid(String value) {
            // Base64 data URI validation - must have valid base64 with proper length
            if (value.startsWith("data:image/")) {
                if (!value.matches("^data:image/[^;]+;base64,[a-zA-Z\\d+/]*(={0,2})$")) {
                    return false;
                }
                // Extract base64 part and validate length
                String base64Part = value.substring(value.indexOf(",") + 1);
                // Base64 must be at least 4 characters
                if (base64Part.length() < 4) {
                    return false;
                }
                // Check proper base64 padding - total length should be multiple of 4
                return base64Part.length() % 4 == 0;
            }
            // URL validation with optional query parameters - ReDoS-resistant version
            return value.matches("^https?://[^?]+\\.(png|jpg|jpeg|gif|bmp)(\\?.*)?$");
        }

        @Override
        public String normalize(String value) {
            return value.trim();
        }
    },
    ARRAY {
        @Override
        public boolean isValid(String value) {
            return value.startsWith("[") || value.contains(",");
        }

        @Override
        public String normalize(String value) {
            return value.trim();
        }
    },
    DATE {
        @Override
        public boolean isValid(String value) {
            try {
                LocalDate.parse(value);
                return true;
            } catch (DateTimeParseException e) {
                return false;
            }
        }

        @Override
        public String normalize(String value) {
            try {
                return LocalDate.parse(value).toString();
            } catch (DateTimeParseException e) {
                // Return original value if parsing fails
                return value;
            }
        }
    },
    DATETIME {
        @Override
        public boolean isValid(String value) {
            try {
                OffsetDateTime dateTime = OffsetDateTime.parse(value);
                // Check timezone offset is within FHIR/ISO-8601 limits: -14:00 to +14:00
                int offsetSeconds = dateTime.getOffset().getTotalSeconds();
                int maxOffsetSeconds = 14 * 60 * 60; // 14 hours in seconds
                return offsetSeconds >= -maxOffsetSeconds && offsetSeconds <= maxOffsetSeconds;
            } catch (DateTimeParseException e) {
                return false;
            }
        }

        @Override
        public String normalize(String value) {
            try {
                return OffsetDateTime.parse(value).toString();
            } catch (DateTimeParseException e) {
                // Return original value if parsing fails
                return value;
            }
        }
    };

    CLAIMSET {
        @Override
        public boolean isValid(String value) {
            if (value == null || value.trim().isEmpty()) {
                return false;
            }

            try {
                UUID.fromString(value.trim());
                return true;
            } catch (IllegalArgumentException e) {
                return false;
            }
        }

        @Override
        public String normalize(String value) {
            if (value == null) {
                throw new IllegalArgumentException("ClaimSet value cannot be null");
            }

            return UUID.fromString(value.trim()).toString();
        }
    };

    public abstract boolean isValid(String value);
    public abstract String normalize(String value);
}
