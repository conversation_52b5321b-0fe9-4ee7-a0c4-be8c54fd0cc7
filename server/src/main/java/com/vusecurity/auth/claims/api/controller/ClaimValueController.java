package com.vusecurity.auth.claims.api.controller;

import com.vusecurity.auth.contracts.api.v1.dto.claims.claimvalue.ClaimValueResponse;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimvalue.CreateClaimValueRequest;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimvalue.UpdateClaimValueRequest;
import com.vusecurity.auth.claims.application.command.CreateClaimValueCommand;
import com.vusecurity.auth.claims.application.command.UpdateClaimValueCommand;
import com.vusecurity.auth.claims.application.command.DeleteClaimValueCommand;
import com.vusecurity.auth.claims.application.handler.CreateClaimValueHandler;
import com.vusecurity.auth.claims.application.handler.UpdateClaimValueHandler;
import com.vusecurity.auth.claims.application.handler.DeleteClaimValueHandler;
import com.vusecurity.auth.claims.application.query.GetClaimValueQuery;
import com.vusecurity.auth.claims.application.query.GetClaimValuesPagedQuery;
import com.vusecurity.auth.claims.application.query.GetClaimValuesByValueQuery;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.claims.mapper.ClaimValueDtoMapper;
import com.vusecurity.auth.contracts.enums.OwnerType;
import com.vusecurity.core.commons.models.PageableResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;
import java.util.UUID;

@RestController
@SecurityRequirement(name = "oauth2 client credentials")
@Tag(name = "Claim Values", description = "Claim value management operations")
@RequestMapping("${app.claims.context-path}")
public class ClaimValueController {

    private final CreateClaimValueHandler createClaimValueHandler;
    private final UpdateClaimValueHandler updateClaimValueHandler;
    private final DeleteClaimValueHandler deleteClaimValueHandler;
    private final GetClaimValueQuery getClaimValueQuery;
    private final ClaimValueDtoMapper claimValueDtoMapper;

    public ClaimValueController(CreateClaimValueHandler createClaimValueHandler,
                               UpdateClaimValueHandler updateClaimValueHandler,
                               DeleteClaimValueHandler deleteClaimValueHandler,
                               GetClaimValueQuery getClaimValueQuery,
                               ClaimValueDtoMapper claimValueDtoMapper) {
        this.createClaimValueHandler = createClaimValueHandler;
        this.updateClaimValueHandler = updateClaimValueHandler;
        this.deleteClaimValueHandler = deleteClaimValueHandler;
        this.getClaimValueQuery = getClaimValueQuery;
        this.claimValueDtoMapper = claimValueDtoMapper;
    }

    @Operation(
            summary = "Get all claim values",
            description = "Retrieves a paginated list of claim values with optional filtering. Supports filtering by various claim value properties."
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Successful retrieval",
                    content = @Content(schema = @Schema(implementation = PageableResponse.class))),
            @ApiResponse(responseCode = "400", description = "Invalid request parameters"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/claim-values")
    public PageableResponse<ClaimValueResponse> get(
            @RequestParam(name = "page", defaultValue = "1") int page,
            @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
            @RequestParam(name = "filter", required = false) Optional<String> filter) {

        GetClaimValuesPagedQuery query = GetClaimValuesPagedQuery.builder()
                .page(page)
                .pageSize(pageSize)
                .filter(filter.orElse(null))
                .build();

        Page<ClaimValueJpaEntity> claimValues = getClaimValueQuery.getAllClaimValues(query);
        return new PageableResponse<>(claimValues.getNumber() + 1, claimValues.getSize(), claimValues.getTotalElements(),
                claimValues.get().map(claimValueDtoMapper::toResponse).toList());
    }

    @Operation(
            summary = "Create a new claim value",
            description = "Creates a new claim value with the specified claim definition, identity/account association, value, and metadata. Either identityId or accountId must be provided."
    )
    @ApiResponses({
            @ApiResponse(responseCode = "201", description = "Claim value created successfully",
                    content = @Content(schema = @Schema(implementation = ClaimValueResponse.class))),
            @ApiResponse(responseCode = "400", description = "Invalid request data or validation errors"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Access denied"),
            @ApiResponse(responseCode = "409", description = "Claim value already exists")
    })
    @PostMapping("/claim-values")
    @ResponseStatus(HttpStatus.CREATED)
    public ClaimValueResponse create(@RequestBody @Valid CreateClaimValueRequest request) {

        CreateClaimValueCommand command = new CreateClaimValueCommand(
                request.getClaimSetId(),
                request.getClaimDefinitionId(),
                request.getIdentityId(),
                request.getAccountId(),
                request.getValue(),
                request.isPrimary(),
                request.isComputed(),
                request.getSource()
        );

        ClaimValueJpaEntity claimValue = createClaimValueHandler.handle(command);
        return claimValueDtoMapper.toResponse(claimValue);
    }


    @Operation(
            summary = "Get claim value by ID",
            description = "Retrieves a specific claim value by its unique identifier."
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Claim value found",
                    content = @Content(schema = @Schema(implementation = ClaimValueResponse.class))),
            @ApiResponse(responseCode = "404", description = "Claim value not found"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/claim-values/{id}")
    public ClaimValueResponse get(@PathVariable(value = "id") UUID id) {

        ClaimValueJpaEntity claimValue = getClaimValueQuery.getClaimValueById(id);
        return claimValueDtoMapper.toResponse(claimValue);
    }

    @Operation(
            summary = "Get claim value by ID and business ID",
            description = "Retrieves a specific claim value by its unique identifier within a business context. Currently returns the claim value by ID only."
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Claim value found",
                    content = @Content(schema = @Schema(implementation = ClaimValueResponse.class))),
            @ApiResponse(responseCode = "404", description = "Claim value not found"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/claim-values/{id}/business/{businessId}")
    public ClaimValueResponse getByIdAndBusinessId(@PathVariable(value = "id") UUID id, @PathVariable(value = "businessId") UUID businessId) {

        // For now, just get by ID. Business validation can be added later when needed
        // TODO: Implement business-scoped validation in a future iteration
        ClaimValueJpaEntity claimValue = getClaimValueQuery.getClaimValueById(id);
        return claimValueDtoMapper.toResponse(claimValue);
    }

    @Operation(
            summary = "Search claim values by value",
            description = "Retrieves a paginated list of claim values that match the specified value. Useful for finding all instances of a particular claim value across the system."
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Claim values found",
                    content = @Content(schema = @Schema(implementation = PageableResponse.class))),
            @ApiResponse(responseCode = "400", description = "Invalid request parameters"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/claim-values/search")
    public PageableResponse<ClaimValueResponse> getByValue(
            @RequestParam(name = "page", defaultValue = "1") int page,
            @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
            @RequestParam(name = "value", required = false) Optional<String> value) {

        GetClaimValuesByValueQuery query = GetClaimValuesByValueQuery.builder()
                .page(page)
                .pageSize(pageSize)
                .value(value.orElse(null))
                .build();

        Page<ClaimValueJpaEntity> claimValues = getClaimValueQuery.getClaimValuesByValue(query);
        return new PageableResponse<>(claimValues.getNumber() + 1, claimValues.getSize(), claimValues.getTotalElements(),
                claimValues.get().map(claimValueDtoMapper::toResponse).toList());
    }

    @Operation(
            summary = "Get related claim values by search value",
            description = "Retrieves all claim values from the same ClaimSet(s) that contain a claim value with the specified search value. " +
                         "This enables hierarchical queries where finding one value returns all related values in the same ClaimSet. " +
                         "For example, searching for 'Trabajo' in a 'tipo' claim would return all address details (calle, ciudad) from the same ClaimSet."
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Related claim values retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request parameters"),
            @ApiResponse(responseCode = "404", description = "Owner not found"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/claim-values/related")
    public List<ClaimValueResponse> getRelatedClaimValuesByValue(
            @RequestParam(name = "ownerType") OwnerType ownerType,
            @RequestParam(name = "ownerId") UUID ownerId,
            @RequestParam(name = "searchValue") String searchValue) {

        List<ClaimValueJpaEntity> relatedValues = getClaimValueQuery.getRelatedClaimValuesByValue(ownerType, ownerId, searchValue);
        return relatedValues.stream()
                .map(claimValueDtoMapper::toResponse)
                .toList();
    }

    @Operation(
            summary = "Delete claim value",
            description = "Deletes a specific claim value by its unique identifier. This action cannot be undone."
    )
    @ApiResponses({
            @ApiResponse(responseCode = "204", description = "Claim value deleted successfully"),
            @ApiResponse(responseCode = "404", description = "Claim value not found"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @DeleteMapping(path = "/claim-values/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void delete(@PathVariable(value = "id") UUID id) {

        DeleteClaimValueCommand command = new DeleteClaimValueCommand(id);
        deleteClaimValueHandler.deleteClaimValue(command);
    }

    @Operation(
            summary = "Update claim value",
            description = "Updates an existing claim value's properties including value, primary status, computed status, and source information."
    )
    @ApiResponses({
            @ApiResponse(responseCode = "204", description = "Claim value updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data or validation errors"),
            @ApiResponse(responseCode = "404", description = "Claim value not found"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PatchMapping(path = "/claim-values/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void update(@PathVariable(value = "id") UUID id, @RequestBody @Valid UpdateClaimValueRequest request) {

        UpdateClaimValueCommand command = new UpdateClaimValueCommand(
                id,
                request.getClaimDefinitionId(),
                request.getIdentityId(),
                request.getAccountId(),
                request.getValue(),
                request.isPrimary() ? Boolean.TRUE : Boolean.FALSE, // Handle primitive boolean
                request.isComputed() ? Boolean.TRUE : Boolean.FALSE, // Handle primitive boolean
                request.getSource(),
                null, // issuedAt not in request
                null  // claimVerificationId not in request
        );

        updateClaimValueHandler.updateClaimValue(command);
    }
}
