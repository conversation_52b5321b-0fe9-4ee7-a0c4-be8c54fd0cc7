package com.vusecurity.auth.contracts.api.v1.dto.identities;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

import java.util.UUID;

/**
 * Only the two permitted subtypes can exist, and <PERSON> figures out which one
 * to instantiate by inspecting the JSON properties.
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.DEDUCTION)
@JsonSubTypes({
        @JsonSubTypes.Type(ScalarClaimValueRequest.class),
        @JsonSubTypes.Type(ArrayClaimValueRequest.class)
})
public sealed interface AccountClaimValueRequest
        permits ScalarClaimValueRequest, ArrayClaimValueRequest {

    @NotNull(message = "Claim Set ID is required")
    @Schema(description = "Unique identifier of the claim set",
            example = "123e4567-e89b-12d3-a456-************")
    UUID claimSetId();

    @NotNull(message = "Claim definition ID is required")
    @Schema(description = "Unique identifier of the claim definition",
            example = "123e4567-e89b-12d3-a456-************")
    UUID claimDefinitionId();
}
