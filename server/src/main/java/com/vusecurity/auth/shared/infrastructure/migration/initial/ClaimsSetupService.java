package com.vusecurity.auth.shared.infrastructure.migration.initial;

import java.util.EnumMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetDefinitionMappingJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.service.ClaimSetDefinitionMappingService;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.ClaimType;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityProviderJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.IdentityProviderRepository;
import com.vusecurity.business.domain.Business;
import com.vusecurity.business.domain.repositories.BusinessRepository;

import jakarta.transaction.Transactional;

@Component
@Transactional
@SuppressWarnings("java:S1192") // long string literals
public class ClaimsSetupService {

    private static final Logger log = LoggerFactory.getLogger(ClaimsSetupService.class);

    private final ClaimDefinitionRepository defRepo;
    private final ClaimSetRepository setRepo;
    private final ClaimSetDefinitionMappingService mappingSvc;
    private final BusinessRepository businessRepo;
    private final IdentityProviderRepository identityProviderRepo;

    public ClaimsSetupService(ClaimDefinitionRepository defRepo,
                              ClaimSetRepository setRepo,
                              ClaimSetDefinitionMappingService mappingSvc,
                              BusinessRepository businessRepo,
                              IdentityProviderRepository identityProviderRepo) {
        this.defRepo = defRepo;
        this.setRepo = setRepo;
        this.mappingSvc = mappingSvc;
        this.businessRepo = businessRepo;
        this.identityProviderRepo = identityProviderRepo;
    }

    /* --------------------------------------------------------------------- */
    /* Kick-off                                                             */
    /* --------------------------------------------------------------------- */
    public void start() {
        log.info("ClaimsSetupService: Starting claims data initialization...");

        Business systemBusiness = businessRepo.findById(DataSeedConstants.SYSTEM_BUSINESS_ID)
                .orElseThrow(() -> new IllegalStateException(
                        "System Business " + DataSeedConstants.SYSTEM_BUSINESS_ID + " not present"));
        log.debug("Using system business: {}", systemBusiness.getId());

        createDefaultDefinitions();
        createDefaultClaimSets(systemBusiness);
        createDefaultIdentityProvider();

        log.info("ClaimsSetupService: Claims data initialization completed successfully");
    }

    /* --------------------------------------------------------------------- */
    /* A. ClaimDefinitions                                                   */
    /* --------------------------------------------------------------------- */
    private void createDefaultDefinitions() {
        long existingCount = defRepo.count();
        log.debug("Current ClaimDefinition count: {}", existingCount);

        if (existingCount > 0) {
            log.info("ClaimDefinition table already populated (count: {}) – skipping.", existingCount);
            return;
        }

        log.info("Creating ClaimDefinitions...");
        Set<ClaimDefinitionJpaEntity> claimDefinitions = createClaimDefinitions();
        claimDefinitions.forEach(d -> d.setCreatedBy("SYSTEM"));
        claimDefinitions.forEach(defRepo::save);
        log.info("Successfully inserted {} ClaimDefinitions.", claimDefinitions.size());
    }

    private Set<ClaimDefinitionJpaEntity> createClaimDefinitions() {
        Set<ClaimDefinitionJpaEntity> claimDefinitionList = new HashSet<>();

        // Core claim definitions
        claimDefinitionList.add(new ClaimDefinitionJpaEntity(DataSeedConstants.CLAIM_DEF_EMAIL_ADDRESSES_ID, "email_addresses", "Email Addresses", "List of user email addresses.", DataTypeEnum.ARRAY, DataSeedConstants.CLAIM_DEF_EMAIL_ADDRESS_ID.toString(), ClaimType.SYSTEM_DEFINED));
        claimDefinitionList.add(new ClaimDefinitionJpaEntity(DataSeedConstants.CLAIM_DEF_EMAIL_ADDRESS_ID, "email_address", "Email", "An email address.", DataTypeEnum.STRING, "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,}$", ClaimType.SYSTEM_DEFINED));
        claimDefinitionList.add(new ClaimDefinitionJpaEntity(DataSeedConstants.CLAIM_DEF_FIRST_NAME_ID, "first_name", "First Name", "User's first name.", DataTypeEnum.STRING, "^[a-zA-Z\\s]+$", ClaimType.SYSTEM_DEFINED));
        claimDefinitionList.add(new ClaimDefinitionJpaEntity(DataSeedConstants.CLAIM_DEF_LAST_NAME_ID, "last_name", "Last Name", "User's last name.", DataTypeEnum.STRING, "^[a-zA-Z\\s]+$", ClaimType.SYSTEM_DEFINED));
        claimDefinitionList.add(new ClaimDefinitionJpaEntity(DataSeedConstants.CLAIM_DEF_PHONE_NUMBERS_ID, "phone_numbers", "Phone Numbers", "List of user phone numbers.", DataTypeEnum.ARRAY, DataSeedConstants.CLAIM_DEF_PHONE_NUMBER_ID.toString(), ClaimType.SYSTEM_DEFINED));
        claimDefinitionList.add(new ClaimDefinitionJpaEntity(DataSeedConstants.CLAIM_DEF_ADDRESSES_ID, "addresses", "Addresses", "List of user postal addresses.", DataTypeEnum.ARRAY, DataSeedConstants.CLAIM_DEF_ADDRESS_ID.toString(), ClaimType.SYSTEM_DEFINED));
        claimDefinitionList.add(new ClaimDefinitionJpaEntity(DataSeedConstants.CLAIM_DEF_LAST_LOGIN_ID, "last_login", "Last Login", "Date and time of user's last login.", DataTypeEnum.DATETIME, "^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d{3})?Z?$", ClaimType.SYSTEM_DEFINED));
        claimDefinitionList.add(new ClaimDefinitionJpaEntity(DataSeedConstants.CLAIM_DEF_PHONE_NUMBER_ID, "phone_number", "Individual Phone Number", "Individual phone number.", DataTypeEnum.STRING, "^(\\+\\d{1,3})?\\d{7,15}$", ClaimType.SYSTEM_DEFINED));
        claimDefinitionList.add(new ClaimDefinitionJpaEntity(DataSeedConstants.CLAIM_DEF_ADDRESS_ID, "address", "Individual Mailing Address", "Individual postal address.", DataTypeEnum.STRING, "^[a-zA-Z0-9\\s.,#-]+$", ClaimType.SYSTEM_DEFINED));
        claimDefinitionList.add(new ClaimDefinitionJpaEntity(DataSeedConstants.CLAIM_DEF_ID_NUMBER_ID, "id_number", "ID Number", "User's official identification number.", DataTypeEnum.STRING, "^[a-zA-Z0-9-]+$", ClaimType.SYSTEM_DEFINED));
        claimDefinitionList.add(new ClaimDefinitionJpaEntity(DataSeedConstants.CLAIM_DEF_SCIM_USER_NAME_ID, "scim_username", "SCIM Username", "SCIM only identification.", DataTypeEnum.STRING, "^[\\S]{1,}[\\s]*$", ClaimType.SYSTEM_DEFINED));
        claimDefinitionList.add(new ClaimDefinitionJpaEntity(DataSeedConstants.CLAIM_DEF_SCIM_EXTERNAL_ID, "scim_external_id", "SCIM External Id", "SCIM external identification.", DataTypeEnum.STRING, "^[\\S]{1,}[\\s]*$", ClaimType.SYSTEM_DEFINED));

        // Workforce-specific claim definitions
        claimDefinitionList.add(new ClaimDefinitionJpaEntity(DataSeedConstants.CLAIM_DEF_EMPLOYEE_ID_ID, "employee_id", "Employee ID", "Unique employee identifier.", DataTypeEnum.STRING, "^EMP[0-9]{6}$", ClaimType.SYSTEM_DEFINED));
        claimDefinitionList.add(new ClaimDefinitionJpaEntity(DataSeedConstants.CLAIM_DEF_DEPARTMENT_ID, "department", "Department", "Employee's department.", DataTypeEnum.STRING, "^[a-zA-Z\\s&-]+$", ClaimType.SYSTEM_DEFINED));
        claimDefinitionList.add(new ClaimDefinitionJpaEntity(DataSeedConstants.CLAIM_DEF_JOB_TITLE_ID, "job_title", "Job Title", "Employee's job title.", DataTypeEnum.STRING, "^[a-zA-Z\\s&-]+$", ClaimType.SYSTEM_DEFINED));
        claimDefinitionList.add(new ClaimDefinitionJpaEntity(DataSeedConstants.CLAIM_DEF_MANAGER_EMAIL_ID, "manager_email", "Manager Email", "Email address of employee's manager.", DataTypeEnum.STRING, "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,}$", ClaimType.SYSTEM_DEFINED));
        claimDefinitionList.add(new ClaimDefinitionJpaEntity(DataSeedConstants.CLAIM_DEF_HIRE_DATE_ID, "hire_date", "Hire Date", "Employee's hire date.", DataTypeEnum.DATE, "^\\d{4}-\\d{2}-\\d{2}$", ClaimType.SYSTEM_DEFINED));

        return claimDefinitionList;
    }

    /* --------------------------------------------------------------------- */
    /* B. ClaimSets  (identifier + profile)                                  */
    /* --------------------------------------------------------------------- */
    private Map<AccountType, List<ClaimSetJpaEntity>> createDefaultClaimSets(Business biz) {

        if (setRepo.count() > 0) {
            log.info("ClaimSet table already populated – skipping.");
            // When loading existing claim sets, we need to fetch them with their mappings
            return setRepo.findAll().stream()
                    .collect(Collectors.groupingBy(ClaimSetJpaEntity::getAccountType));
        }

        Map<String, ClaimDefinitionJpaEntity> claimDefinitionsByCode =
                defRepo.findAll().stream().collect(Collectors.toMap(ClaimDefinitionJpaEntity::getCode, Function.identity()));

        Map<AccountType, List<ClaimSetJpaEntity>> result = new EnumMap<>(AccountType.class);

        for (AccountType type : AccountType.values()) {

            /* 1. Identifier set */
            ClaimSetJpaEntity identifierClaimSet = new ClaimSetJpaEntity(
                    getIdentifierClaimSetId(type), biz, type, true,
                    type.name() + " Identifier Set", "Claims that uniquely identify an account");
            identifierClaimSet.setCreatedBy("SYSTEM");

            /* 2. Profile set */
            ClaimSetJpaEntity profileClaimSet = new ClaimSetJpaEntity(
                    getProfileClaimSetId(type), biz, type, false,
                    type.name() + " Profile Set", "Non-identifier profile / preference claims");
            profileClaimSet.setCreatedBy("SYSTEM");

            // Save the claim sets first
            identifierClaimSet = setRepo.save(identifierClaimSet);
            profileClaimSet = setRepo.save(profileClaimSet);

            // Create the mappings
            mapDefinitions(type, identifierClaimSet, profileClaimSet, claimDefinitionsByCode);

            // Refresh the entities to get the updated mappings
            identifierClaimSet = setRepo.findById(identifierClaimSet.getId()).orElse(identifierClaimSet);
            profileClaimSet = setRepo.findById(profileClaimSet.getId()).orElse(profileClaimSet);

            result.put(type, List.of(identifierClaimSet, profileClaimSet));
        }

        log.info("Inserted {} ClaimSets (identifier+profile for each AccountType).", result.size() * 2);
        return result;
    }

    private void mapDefinitions(AccountType t,
                                ClaimSetJpaEntity identifierClaimSet,
                                ClaimSetJpaEntity profileClaimSet,
                                Map<String, ClaimDefinitionJpaEntity> claimDefinitionsByCode) {

        Set<String> idClaims = IDENTIFIER_CLAIMS.getOrDefault(t, Set.of());
        Set<String> profileClaims = PROFILE_CLAIMS.getOrDefault(t, Set.of());

        log.info("Mapping definitions for account type {}: identifier claims = {}, profile claims = {}",
                t, idClaims, profileClaims);
        log.info("Available claim definition codes: {}", claimDefinitionsByCode.keySet());

        idClaims.forEach(code -> {
            ClaimDefinitionJpaEntity claimDef = claimDefinitionsByCode.get(code);
            if (claimDef != null) {
                ClaimSetDefinitionMappingJpaEntity mapping = mappingSvc.addClaimDefinitionToClaimSet(identifierClaimSet, claimDef, null, true);
                log.info("Created identifier mapping: {} -> {}", code, mapping.getId());
            } else {
                log.warn("Claim definition with code '{}' not found for identifier claims of account type {}", code, t);
            }
        });

        profileClaims.forEach(code -> {
            ClaimDefinitionJpaEntity claimDef = claimDefinitionsByCode.get(code);
            if (claimDef != null) {
                ClaimSetDefinitionMappingJpaEntity mapping = mappingSvc.addClaimDefinitionToClaimSet(profileClaimSet, claimDef, null, false);
                log.info("Created profile mapping: {} -> {}", code, mapping.getId());
            } else {
                log.warn("Claim definition with code '{}' not found for profile claims of account type {}", code, t);
            }
        });
    }

    private IdentityProviderJpaEntity createDefaultIdentityProvider() {
        return identityProviderRepo.findAll().stream()
                .findFirst()
                .orElseGet(() -> {
                    IdentityProviderJpaEntity provider = new IdentityProviderJpaEntity("LOCAL");
                    provider.setCreatedBy("SEEDER");
                    provider.activate();
                    return identityProviderRepo.save(provider);
                });
    }

    /* --------------------------------------------------------------------- */
    /* Static maps with business semantics                                   */
    /* --------------------------------------------------------------------- */
    private static final Map<AccountType, Set<String>> IDENTIFIER_CLAIMS = Map.of(
            AccountType.CUSTOMER, Set.of("email_address"),
            AccountType.FEDERATED, Set.of("email_address"),
            AccountType.WORKFORCE, Set.of("email_address"),
            AccountType.SERVICE, Set.of("email_address"),
            AccountType.SOCIAL, Set.of("email_address"),
            AccountType.LOCAL, Set.of("email_address")
    );

    private static final Map<AccountType, Set<String>> PROFILE_CLAIMS = Map.of(
            AccountType.CUSTOMER, Set.of("first_name", "last_name", "phone_number"),
            AccountType.FEDERATED, Set.of("first_name", "last_name", "email_address", "scim_username", "scim_external_id"),
            AccountType.WORKFORCE, Set.of("first_name", "last_name", "phone_numbers", "department", "job_title", "manager_email", "hire_date"),
            AccountType.SERVICE, Set.of("id_number"),
            AccountType.SOCIAL, Set.of("first_name", "last_name", "phone_number"),
            AccountType.LOCAL, Set.of("first_name", "last_name", "last_login")
    );

    /* --------------------------------------------------------------------- */
    /* Helper methods for UUID constants                                     */
    /* --------------------------------------------------------------------- */
    private static UUID getIdentifierClaimSetId(AccountType type) {
        return switch (type) {
            case NONE -> DataSeedConstants.NONE_IDENTIFIER_CLAIM_SET_ID;
            case CUSTOMER -> DataSeedConstants.CUSTOMER_IDENTIFIER_CLAIM_SET_ID;
            case FEDERATED -> DataSeedConstants.FEDERATED_IDENTIFIER_CLAIM_SET_ID;
            case WORKFORCE -> DataSeedConstants.WORKFORCE_IDENTIFIER_CLAIM_SET_ID;
            case SERVICE -> DataSeedConstants.SERVICE_IDENTIFIER_CLAIM_SET_ID;
            case SOCIAL -> DataSeedConstants.SOCIAL_IDENTIFIER_CLAIM_SET_ID;
            case LOCAL -> DataSeedConstants.LOCAL_IDENTIFIER_CLAIM_SET_ID;
        };
    }

    private static UUID getProfileClaimSetId(AccountType type) {
        return switch (type) {
            case NONE -> DataSeedConstants.NONE_PROFILE_CLAIM_SET_ID;
            case CUSTOMER -> DataSeedConstants.CUSTOMER_PROFILE_CLAIM_SET_ID;
            case FEDERATED -> DataSeedConstants.FEDERATED_PROFILE_CLAIM_SET_ID;
            case WORKFORCE -> DataSeedConstants.WORKFORCE_PROFILE_CLAIM_SET_ID;
            case SERVICE -> DataSeedConstants.SERVICE_PROFILE_CLAIM_SET_ID;
            case SOCIAL -> DataSeedConstants.SOCIAL_PROFILE_CLAIM_SET_ID;
            case LOCAL -> DataSeedConstants.LOCAL_PROFILE_CLAIM_SET_ID;
        };
    }


}