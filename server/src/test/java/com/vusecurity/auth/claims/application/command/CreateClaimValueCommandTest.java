package com.vusecurity.auth.claims.application.command;

import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for CreateClaimValueCommand validation logic.
 * Tests pure domain logic with no external dependencies.
 */
class CreateClaimValueCommandTest {

    @Test
    void shouldValidateSuccessfully_WhenAllRequiredFieldsProvidedWithIdentityId() {
        // Given
        CreateClaimValueCommand command = new CreateClaimValueCommand(
                UUID.randomUUID(), // claimDefinitionId
                UUID.randomUUID(), // identityId
                null,              // accountId
                "<EMAIL>",
                true,              // isPrimary
                false,             // isComputed
                "USER_INPUT"
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldValidateSuccessfully_WhenAllRequiredFieldsProvidedWithAccountId() {
        // Given
        CreateClaimValueCommand command = new CreateClaimValueCommand(
                UUID.randomUUID(), // claimSetId
                UUID.randomUUID(), // claimDefinitionId
                null,              // identityId
                UUID.randomUUID(), // accountId
                "<EMAIL>",
                true,              // isPrimary
                false,             // isComputed
                "USER_INPUT"
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldValidateSuccessfully_WhenOptionalFieldsAreNull() {
        // Given
        CreateClaimValueCommand command = new CreateClaimValueCommand(
                UUID.randomUUID(), // claimDefinitionId
                UUID.randomUUID(), // identityId
                null,              // accountId
                "<EMAIL>",
                true,              // isPrimary
                false,             // isComputed
                null              // source is optional
        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldThrowException_WhenClaimDefinitionIdIsNull() {
        // Given
        CreateClaimValueCommand command = new CreateClaimValueCommand(
                null,              // claimDefinitionId is null
                UUID.randomUUID(), // identityId
                null,              // accountId
                "<EMAIL>",
                true,              // isPrimary
                false,             // isComputed
                "USER_INPUT"
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("claimDefinitionId is required", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenBothIdentityIdAndAccountIdAreNull() {
        // Given
        CreateClaimValueCommand command = new CreateClaimValueCommand(
                UUID.randomUUID(), // claimDefinitionId
                null,              // identityId is null
                null,              // accountId is null
                "<EMAIL>",
                true,              // isPrimary
                false,             // isComputed
                "USER_INPUT"
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("Either identityId or accountId is required", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenBothIdentityIdAndAccountIdAreProvided() {
        // Given
        CreateClaimValueCommand command = new CreateClaimValueCommand(
                UUID.randomUUID(), // claimDefinitionId
                UUID.randomUUID(), // identityId is provided
                UUID.randomUUID(), // accountId is also provided
                "<EMAIL>",
                true,              // isPrimary
                false,             // isComputed
                "USER_INPUT"
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("Cannot specify both identityId and accountId", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenValueIsNull() {
        // Given
        CreateClaimValueCommand command = new CreateClaimValueCommand(
                UUID.randomUUID(), // claimDefinitionId
                UUID.randomUUID(), // identityId
                null,              // accountId
                null,              // value is null
                true,              // isPrimary
                false,             // isComputed
                "USER_INPUT"
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("value is required", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenValueIsEmpty() {
        // Given
        CreateClaimValueCommand command = new CreateClaimValueCommand(
                UUID.randomUUID(), // claimDefinitionId
                UUID.randomUUID(), // identityId
                null,              // accountId
                "",                // value is empty
                true,              // isPrimary
                false,             // isComputed
                "USER_INPUT"
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("value is required", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenValueIsWhitespace() {
        // Given
        CreateClaimValueCommand command = new CreateClaimValueCommand(
                UUID.randomUUID(), // claimDefinitionId
                UUID.randomUUID(), // identityId
                null,              // accountId
                "   ",             // value is whitespace
                true,              // isPrimary
                false,             // isComputed
                "USER_INPUT"
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("value is required", exception.getMessage());
    }

    @Test
    void shouldValidateSuccessfully_WithMinimalRequiredFields() {
        // Given
        CreateClaimValueCommand command = new CreateClaimValueCommand(
                UUID.randomUUID(), // claimDefinitionId
                UUID.randomUUID(), // identityId
                null,              // accountId
                "test-value",      // value
                false,             // isPrimary
                false,             // isComputed
                null               // source

        );

        // When & Then
        assertDoesNotThrow(command::validate);
    }

    @Test
    void shouldValidateSuccessfully_WithBooleanFlags() {
        // Test different combinations of boolean flags
        boolean[] booleanValues = {true, false};

        for (boolean isPrimary : booleanValues) {
            for (boolean isComputed : booleanValues) {
                // Given
                CreateClaimValueCommand command = new CreateClaimValueCommand(
                        UUID.randomUUID(), // claimDefinitionId
                        UUID.randomUUID(), // identityId
                        null,              // accountId
                        "test-value",
                        isPrimary,
                        isComputed,
                        "TEST_SOURCE"
                );

                // When & Then
                assertDoesNotThrow(command::validate,
                        String.format("Validation should pass for isPrimary=%s, isComputed=%s",
                                isPrimary, isComputed));
            }
        }
    }

    @Test
    void shouldValidateSuccessfully_WithDifferentValueTypes() {
        // Test with different value formats
        String[] testValues = {
                "simple-text",
                "<EMAIL>",
                "123456",
                "true",
                "2023-12-25",
                "Complex value with spaces and symbols!@#$%",
                "Multi\nline\nvalue"
        };

        for (String value : testValues) {
            // Given
            CreateClaimValueCommand command = new CreateClaimValueCommand(
                    UUID.randomUUID(), // claimDefinitionId
                    UUID.randomUUID(), // identityId
                    null,              // accountId
                    value,
                    true,              // isPrimary
                    false,             // isComputed
                    "USER_INPUT"
            );

            // When & Then
            assertDoesNotThrow(command::validate,
                    "Validation should pass for value: " + value);
        }
    }

    @Test
    void shouldValidateSuccessfully_WithDifferentSources() {
        // Test with different source values
        String[] testSources = {
                "USER_INPUT",
                "SYSTEM_GENERATED",
                "EXTERNAL_API",
                "COMPUTED",
                null // source is optional
        };

        for (String source : testSources) {
            // Given
            CreateClaimValueCommand command = new CreateClaimValueCommand(
                    UUID.randomUUID(), // claimDefinitionId
                    UUID.randomUUID(), // identityId
                    null,              // accountId
                    "test-value",
                    true,              // isPrimary
                    false,             // isComputed
                    source
            );

            // When & Then
            assertDoesNotThrow(command::validate,
                    "Validation should pass for source: " + source);
        }
    }

    @Test
    void should_validate_against_regex_pattern() {
        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                null,
                "<EMAIL>",
                true,
                false,
                "USER_INPUT"
        );

        String emailRegex = "^[\\w-.]+@([\\w-]+\\.)+[\\w-]{2,4}$";
        assertDoesNotThrow(() -> cmd.validateWithPattern(emailRegex));
    }

    @Test
    void should_fail_if_regex_does_not_match() {
        CreateClaimValueCommand  cmd = new CreateClaimValueCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                null,
                "not-an-email",
                true,
                false,
                "USER_INPUT"
        );

        String emailRegex = "^[\\w-.]+@([\\w-]+\\.)+[\\w-]{2,4}$";
        assertThrows(IllegalArgumentException.class, () -> cmd.validateWithPattern(emailRegex));
    }

    @Test
    void should_validate_against_phone_number_pattern() {
        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                null,
                "******-123-4567",
                true,
                false,
                "USER_INPUT"
        );

        String phoneRegex = "^\\+?[1-9]\\d?-?\\d{3}-\\d{3}-\\d{4}$";
        assertDoesNotThrow(() -> cmd.validateWithPattern(phoneRegex));
    }

    @Test
    void should_validate_against_employee_id_pattern() {
        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                null,
                "EMP-2023-001",
                true,
                false,
                "SYSTEM_GENERATED"
        );

        String empIdRegex = "^EMP-\\d{4}-\\d{3}$";
        assertDoesNotThrow(() -> cmd.validateWithPattern(empIdRegex));
    }

    @Test
    void should_pass_validation_when_pattern_is_null() {
        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                null,
                "any-value",
                true,
                false,
                "USER_INPUT"
        );

        assertDoesNotThrow(() -> cmd.validateWithPattern(null));
    }

    @Test
    void should_pass_validation_when_pattern_is_blank() {
        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                null,
                "any-value",
                true,
                false,
                "USER_INPUT"
        );

        assertDoesNotThrow(() -> cmd.validateWithPattern(""));
        assertDoesNotThrow(() -> cmd.validateWithPattern("   "));
    }

    @Test
    void should_validate_complex_regex_patterns() {
        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                null,
                "<EMAIL>",
                true,
                false,
                "USER_INPUT"
        );

        // Complex email regex
        String complexEmailRegex = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        assertDoesNotThrow(() -> cmd.validateWithPattern(complexEmailRegex));
    }

    @Test
    void should_fail_validation_with_descriptive_error_message() {
        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                UUID.randomUUID(),
                UUID.randomUUID(),
                null,
                "invalid-format",
                true,
                false,
                "USER_INPUT"
        );

        String pattern = "^[0-9]+$";
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> cmd.validateWithPattern(pattern)
        );
        assertEquals("value does not match the required pattern", exception.getMessage());
    }

    @Test
    void shouldThrowException_WhenClaimSetIdIsNullAndAccountIdIsProvided() {
        // Given
        CreateClaimValueCommand command = new CreateClaimValueCommand(
                null,              //claimSetId is null              
                UUID.randomUUID(),              // claimDefinitionId
                null,              // identityId
                UUID.randomUUID(),              // accountId
                "<EMAIL>",
                true,              // isPrimary
                false,             // isComputed
                "USER_INPUT"
        );

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                command::validate
        );
        assertEquals("claimSetId is required for accounts", exception.getMessage());
    }

}
