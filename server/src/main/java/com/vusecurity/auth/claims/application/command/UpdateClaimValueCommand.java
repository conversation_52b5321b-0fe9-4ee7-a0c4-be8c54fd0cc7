package com.vusecurity.auth.claims.application.command;

import java.time.Instant;
import java.util.UUID;

public record UpdateClaimValueCommand(
        UUID id,
        UUID claimSetId,
        UUID claimDefinitionId,
        UUID identityId,
        UUID accountId,
        String value,
        Boolean isPrimary,
        Boolean isComputed,
        String source,
        Instant issuedAt,
        UUID claimVerificationId) {

        public UpdateClaimValueCommand(
            UUID id,
            UUID claimDefinitionId,
            UUID identityId,
            UUID accountId,
            String value,
            Boolean isPrimary,
            Boolean isComputed,
            String source,
            Instant issuedAt,
            UUID claimVerificationId) {
                this(id, null, claimDefinitionId, identityId, accountId, value, isPrimary, isComputed, source, issuedAt, claimVerificationId);
        }

    public void validate() {
        if (id == null) {
            throw new IllegalArgumentException("id is required");
        }
    }

    public void validateWithPattern(String pattern) {
        if (pattern != null && !pattern.isBlank() && value != null && !value.matches(pattern)) {
            throw new IllegalArgumentException("value does not match the required pattern");
        }
    }
}
