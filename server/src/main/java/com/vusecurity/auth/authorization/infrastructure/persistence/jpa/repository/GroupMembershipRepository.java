package com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository;

import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.GroupMembershipJpaEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Spring Data JPA repository for GroupMembershipJpaEntity.
 * Infrastructure-only interface for database operations.
 */
@Repository
public interface GroupMembershipRepository extends JpaRepository<GroupMembershipJpaEntity, UUID>, JpaSpecificationExecutor<GroupMembershipJpaEntity> {

    /**
     * Find membership by account ID and group ID.
     *
     * @param accountId the account ID
     * @param groupId   the group ID
     * @return the membership if found
     */
    @Query("SELECT gm FROM GroupMembershipJpaEntity gm WHERE gm.account.id = :accountId AND gm.group.id = :groupId")
    Optional<GroupMembershipJpaEntity> findByAccountIdAndGroupId(@Param("accountId") UUID accountId, @Param("groupId") UUID groupId);

    /**
     * Find all memberships for a specific account.
     *
     * @param accountId the account ID
     * @return list of memberships for the account
     */
    @Query("SELECT gm FROM GroupMembershipJpaEntity gm WHERE gm.account.id = :accountId AND gm.isActive = true")
    List<GroupMembershipJpaEntity> findByAccountId(@Param("accountId") UUID accountId);

    /**
     * Find all memberships for a specific group.
     *
     * @param groupId the group ID
     * @return list of memberships for the group
     */
    @Query("SELECT gm FROM GroupMembershipJpaEntity gm WHERE gm.group.id = :groupId AND gm.isActive = true")
    List<GroupMembershipJpaEntity> findByGroupId(@Param("groupId") UUID groupId);

    /**
     * Find all active memberships for a specific account.
     *
     * @param accountId the account ID
     * @return list of active memberships for the account
     */
    @Query("SELECT gm FROM GroupMembershipJpaEntity gm WHERE gm.account.id = :accountId AND gm.isActive = true")
    List<GroupMembershipJpaEntity> findActiveByAccountId(@Param("accountId") UUID accountId);

    /**
     * Find all active memberships for a specific group.
     *
     * @param groupId the group ID
     * @return list of active memberships for the group
     */
    @Query("SELECT gm FROM GroupMembershipJpaEntity gm WHERE gm.group.id = :groupId AND gm.isActive = true")
    List<GroupMembershipJpaEntity> findActiveByGroupId(@Param("groupId") UUID groupId);

    /**
     * Check if an account is a member of a specific group.
     *
     * @param accountId the account ID
     * @param groupId   the group ID
     * @return true if the account is an active member of the group
     */
    @Query("SELECT COUNT(gm) > 0 FROM GroupMembershipJpaEntity gm WHERE gm.account.id = :accountId AND gm.group.id = :groupId AND gm.isActive = true")
    boolean existsByAccountIdAndGroupIdAndActive(@Param("accountId") UUID accountId, @Param("groupId") UUID groupId);

    /**
     * Count active members in a group.
     *
     * @param groupId the group ID
     * @return count of active members
     */
    @Query("SELECT COUNT(gm) FROM GroupMembershipJpaEntity gm WHERE gm.group.id = :groupId AND gm.isActive = true")
    long countActiveByGroupId(@Param("groupId") UUID groupId);

    /**
     * Delete all memberships for a specific group.
     *
     * @param groupId the group ID
     */
    @Modifying
    @Query("DELETE FROM GroupMembershipJpaEntity gm WHERE gm.group.id = :groupId")
    void deleteByGroupId(@Param("groupId") UUID groupId);

    /**
     * Delete all memberships for a specific account.
     *
     * @param accountId the account ID
     */
    @Modifying
    @Query("DELETE FROM GroupMembershipJpaEntity gm WHERE gm.account.id = :accountId")
    void deleteByAccountId(@Param("accountId") UUID accountId);

    /**
     * Deletes all memberships for a given group and a list of account IDs.
     *
     * @param groupId    the group ID
     * @param accountIds the list of account IDs
     */
    @Modifying
    @Query("DELETE FROM GroupMembershipJpaEntity gm WHERE gm.group.id = :groupId AND gm.account.id IN :accountIds")
    void deleteByGroupIdAndAccountIdIn(@Param("groupId") UUID groupId, @Param("accountIds") List<UUID> accountIds);
} 