package com.vusecurity.auth.shared.infrastructure.migration;

import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.env.Environment;
import org.springframework.test.util.ReflectionTestUtils;

import com.vusecurity.auth.shared.infrastructure.migration.initial.ClaimsSetupService;
import com.vusecurity.auth.shared.infrastructure.migration.initial.OAuth2RegisteredClientSetupService;
import com.vusecurity.auth.shared.infrastructure.migration.initial.RolesAndPermissionsSetupService;
import com.vusecurity.auth.shared.infrastructure.migration.initial.SystemSetupService;
import com.vusecurity.multitenant.jpa.hibernate.events.DataSourceCreatedEvent;
import com.zaxxer.hikari.HikariDataSource;

@ExtendWith(MockitoExtension.class)
class DataSeedSecurityTest {

    @Mock
    private ClaimsSetupService claimsSetupService;

    @Mock
    private RolesAndPermissionsSetupService rolesAndPermissionsSetupService;
    
    @Mock
    private OAuth2RegisteredClientSetupService oAuth2RegisteredClientSetupService;

    @Mock
    private SystemSetupService systemSetupService;
    
    @Mock
    private Environment environment;
    
    @Mock
    private HikariDataSource dataSource;
    
    @Mock
    private DataSourceCreatedEvent event;
    
    private MultitenantDataSourceGeneratedListener listener;

    @BeforeEach
    void setUp() {
        listener = new MultitenantDataSourceGeneratedListener(
            rolesAndPermissionsSetupService,
            claimsSetupService, 
            systemSetupService,
            oAuth2RegisteredClientSetupService,
            environment
        );
        
        when(event.getDataSource()).thenReturn(dataSource);
        when(dataSource.getPoolName()).thenReturn("test-pool");
    }

    @Test
    void shouldSkipDataSeedingWhenDisabledByDefault() {
        // Given: No environment variables set, default property is false
        when(environment.getProperty("ENABLE_DATA_SEEDING")).thenReturn(null);
        ReflectionTestUtils.setField(listener, "dataSeedEnabled", false);

        // When: Event is triggered
        listener.onApplicationEvent(event);

        // Then: No setup services should be called
        verify(systemSetupService, never()).start();
        verify(claimsSetupService, never()).start();
        verify(rolesAndPermissionsSetupService, never()).start();
    }

    @Test
    void shouldEnableDataSeedingWhenEnvironmentVariableIsTrue() {
        // Given: ENABLE_DATA_SEEDING environment variable is set to true
        when(environment.getProperty("ENABLE_DATA_SEEDING")).thenReturn("true");

        // When: Event is triggered
        listener.onApplicationEvent(event);

        // Then: All setup services should be called
        verify(systemSetupService).start();
        verify(claimsSetupService).start();
        verify(rolesAndPermissionsSetupService).start();
    }

    @Test
    void shouldEnableDataSeedingWhenApplicationPropertyIsTrue() {
        // Given: No environment variables set, but application property is true
        when(environment.getProperty("ENABLE_DATA_SEEDING")).thenReturn(null);
        ReflectionTestUtils.setField(listener, "dataSeedEnabled", true);

        // When: Event is triggered
        listener.onApplicationEvent(event);

        // Then: All setup services should be called
        verify(systemSetupService).start();
        verify(claimsSetupService).start();
        verify(rolesAndPermissionsSetupService).start();
    }

    @Test
    void shouldPrioritizeEnvironmentVariableOverApplicationProperty() {
        // Given: Environment variable is false, but application property is true
        when(environment.getProperty("ENABLE_DATA_SEEDING")).thenReturn("false");
        ReflectionTestUtils.setField(listener, "dataSeedEnabled", true);

        // When: Event is triggered
        listener.onApplicationEvent(event);

        // Then: No setup services should be called (environment variable takes priority)
        verify(systemSetupService, never()).start();
        verify(claimsSetupService, never()).start();
        verify(rolesAndPermissionsSetupService, never()).start();
    }
}
