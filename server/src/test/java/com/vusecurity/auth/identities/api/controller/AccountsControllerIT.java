package com.vusecurity.auth.identities.api.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetDefinitionMappingJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetDefinitionMappingRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimValueRepository;
import com.vusecurity.auth.contracts.api.v1.dto.identities.ArrayClaimValueRequest;
import com.vusecurity.auth.contracts.api.v1.dto.identities.ClaimIdentifierRequest;
import com.vusecurity.auth.contracts.api.v1.dto.identities.CreateAccountRequest;
import com.vusecurity.auth.contracts.api.v1.dto.identities.ScalarClaimValueRequest;
import com.vusecurity.auth.contracts.api.v1.dto.identities.UpdateAccountRequest;
import com.vusecurity.auth.contracts.enums.AccountLifecycleState;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.ClaimType;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import com.vusecurity.auth.contracts.enums.IdentityType;
import com.vusecurity.auth.contracts.enums.OwnerType;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityProviderJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.IdentityProviderRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.IdentityRepository;
import com.vusecurity.auth.shared.TestDataBuilder;
import com.vusecurity.auth.shared.infrastructure.migration.initial.DataSeedConstants;
import com.vusecurity.auth.shared.test.BaseIntegrationTest;
import com.vusecurity.business.domain.Business;
import com.vusecurity.business.domain.enums.BusinessTypeEnum;
import com.vusecurity.business.domain.repositories.BusinessRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.context.WebApplicationContext;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.util.List;
import java.util.Map;
import java.util.UUID;

import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.test.web.servlet.setup.MockMvcBuilders.webAppContextSetup;

/**
 * Integration tests for AccountsController covering all endpoints and edge cases.
 * Tests include account creation with and without claim values, validation scenarios,
 * and duplicate account handling.
 */
@AutoConfigureWebMvc
@Testcontainers
class AccountsControllerIT extends BaseIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private BusinessRepository businessRepository;

    @Autowired
    private IdentityRepository identityRepository;

    @Autowired
    private AccountRepository accountRepository;

    @Autowired
    private IdentityProviderRepository identityProviderRepository;

    @Autowired
    private ClaimDefinitionRepository claimDefinitionRepo;

    @Autowired
    private ClaimSetRepository claimSetRepo;

    @Autowired
    private ClaimSetDefinitionMappingRepository mappingRepo;

    @Autowired
    private ClaimValueRepository claimValueRepo;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;
    private Business business;
    private IdentityJpaEntity identity;
    private IdentityProviderJpaEntity identityProvider;
    
    // Test claim definitions
    private ClaimDefinitionJpaEntity employeeIdClaimDef;
    private ClaimDefinitionJpaEntity departmentClaimDef;
    private ClaimDefinitionJpaEntity phoneNumbersClaimDef;
    private ClaimDefinitionJpaEntity phoneNumberClaimDef;
    
    // Test claim set
    private ClaimSetJpaEntity workforceProfileClaimSet;
    private ClaimSetJpaEntity workforceIdentifierClaimSet;

    @BeforeEach
    void setUp() {
        mockMvc = webAppContextSetup(webApplicationContext).build();

        // Create test business
        business = new Business()
                .setName("Test Business")
                .setBusinessType(BusinessTypeEnum.BUSINESS_UNIT);
        business.setCreatedBy("test");
        business = businessRepository.save(business);

        // Create test identity provider - use the seeded one if available
        identityProvider = identityProviderRepository.findById(DataSeedConstants.IDENTITY_PROVIDER_ID)
                .orElseGet(() -> {
                    IdentityProviderJpaEntity provider = new IdentityProviderJpaEntity("Test Provider");
                    provider.setCreatedBy("test");
                    return identityProviderRepository.save(provider);
                });

        // Create test identity
        identity = new IdentityJpaEntity(IdentityType.PERSON, "<EMAIL>");
        identity.setCreatedBy("test");
        identity = identityRepository.save(identity);

        // Create test claim definitions
        createTestClaimDefinitions();
        
        // Create test claim set and mappings
        createTestClaimSet();
    }

    private void createTestClaimDefinitions() {
        // Employee ID claim definition
        employeeIdClaimDef = new ClaimDefinitionJpaEntity(
                DataSeedConstants.CLAIM_DEF_EMPLOYEE_ID_ID,
                "employee_id",
                "Employee ID",
                "Unique employee identifier",
                DataTypeEnum.STRING,
                "^EMP[0-9]{6}$",
                ClaimType.SYSTEM_DEFINED
        );
        employeeIdClaimDef.setCreatedBy("test");
        employeeIdClaimDef = claimDefinitionRepo.save(employeeIdClaimDef);

        // Department claim definition
        departmentClaimDef = new ClaimDefinitionJpaEntity(
                DataSeedConstants.CLAIM_DEF_DEPARTMENT_ID,
                "department",
                "Department",
                "Employee's department",
                DataTypeEnum.STRING,
                "^[a-zA-Z\\s&-]+$",
                ClaimType.SYSTEM_DEFINED
        );
        departmentClaimDef.setCreatedBy("test");
        departmentClaimDef = claimDefinitionRepo.save(departmentClaimDef);

        // Phone numbers array claim definition
        phoneNumbersClaimDef = new ClaimDefinitionJpaEntity(
                DataSeedConstants.CLAIM_DEF_PHONE_NUMBERS_ID,
                "phone_numbers",
                "Phone Numbers",
                "List of user phone numbers",
                DataTypeEnum.ARRAY,
                DataSeedConstants.CLAIM_DEF_PHONE_NUMBER_ID.toString(),
                ClaimType.SYSTEM_DEFINED
        );
        phoneNumbersClaimDef.setCreatedBy("test");
        phoneNumbersClaimDef = claimDefinitionRepo.save(phoneNumbersClaimDef);

        // Individual phone number claim definition (for array elements)
        phoneNumberClaimDef = new ClaimDefinitionJpaEntity(
                DataSeedConstants.CLAIM_DEF_PHONE_NUMBER_ID,
                "phone_number",
                "Phone Number",
                "Individual phone number",
                DataTypeEnum.STRING,
                "^(\\+\\d{1,3})?\\d{7,15}$",
                ClaimType.SYSTEM_DEFINED
        );
        phoneNumberClaimDef.setCreatedBy("test");
        phoneNumberClaimDef = claimDefinitionRepo.save(phoneNumberClaimDef);
    }

    private void createTestClaimSet() {
        //Create workforce identifier claim set
        workforceIdentifierClaimSet = new ClaimSetJpaEntity(
                DataSeedConstants.WORKFORCE_IDENTIFIER_CLAIM_SET_ID,
                business,
                AccountType.WORKFORCE,
                true,
                "WORKFORCE Identifier Set",
                "Identifier profile claims for workforce accounts"
        );
        workforceIdentifierClaimSet.setCreatedBy("test");
        workforceIdentifierClaimSet = claimSetRepo.save(workforceIdentifierClaimSet);

        // Create mappings between claim set and claim definitions
        ClaimSetDefinitionMappingJpaEntity employeeIdIdentityMapping = new ClaimSetDefinitionMappingJpaEntity(
                workforceIdentifierClaimSet,
                employeeIdClaimDef
        );
        employeeIdIdentityMapping.setClaimDefinitionOrder(1);
        employeeIdIdentityMapping.setEnforceUniqueness(true);
        employeeIdIdentityMapping.setCreatedBy("test");
        mappingRepo.save(employeeIdIdentityMapping);
    
        // Create workforce profile claim set
        workforceProfileClaimSet = new ClaimSetJpaEntity(
                DataSeedConstants.WORKFORCE_PROFILE_CLAIM_SET_ID,
                business,
                AccountType.WORKFORCE,
                false,
                "WORKFORCE Profile Set",
                "Non-identifier profile claims for workforce accounts"
        );
        workforceProfileClaimSet.setCreatedBy("test");
        workforceProfileClaimSet = claimSetRepo.save(workforceProfileClaimSet);

        // Create mappings between claim set and claim definitions
        ClaimSetDefinitionMappingJpaEntity employeeIdMapping = new ClaimSetDefinitionMappingJpaEntity(
                workforceProfileClaimSet,
                employeeIdClaimDef
        );
        employeeIdMapping.setClaimDefinitionOrder(1);
        employeeIdMapping.setEnforceUniqueness(true);
        employeeIdMapping.setCreatedBy("test");
        mappingRepo.save(employeeIdMapping);

        ClaimSetDefinitionMappingJpaEntity departmentMapping = new ClaimSetDefinitionMappingJpaEntity(
                workforceProfileClaimSet,
                departmentClaimDef
        );
        departmentMapping.setClaimDefinitionOrder(2);
        departmentMapping.setEnforceUniqueness(false);
        departmentMapping.setCreatedBy("test");
        mappingRepo.save(departmentMapping);

        ClaimSetDefinitionMappingJpaEntity phoneNumbersMapping = new ClaimSetDefinitionMappingJpaEntity(
                workforceProfileClaimSet,
                phoneNumbersClaimDef
        );
        phoneNumbersMapping.setClaimDefinitionOrder(3);
        phoneNumbersMapping.setEnforceUniqueness(false);
        phoneNumbersMapping.setCreatedBy("test");
        mappingRepo.save(phoneNumbersMapping);
    }

    @Test
    void shouldCreateAccountSuccessfully_WithoutClaimValues() throws Exception {
        // Given
        CreateAccountRequest request = new CreateAccountRequest();
        request.setAccountType(AccountType.WORKFORCE);
        request.setBusinessId(business.getId());
        request.setIdentityProviderId(identityProvider.getId());
        request.setIdentityId(identity.getId());

        // When & Then
        mockMvc.perform(post("/api/v1/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.businessId").value(business.getId().toString()))
                .andExpect(jsonPath("$.identityId").value(identity.getId().toString()))
                .andExpect(jsonPath("$.identityProviderId").value(identityProvider.getId().toString()))
                .andExpect(jsonPath("$.accountType").value(AccountType.WORKFORCE.toString()))
                .andExpect(jsonPath("$.lifecycleState").value("PENDING"))
                .andExpect(jsonPath("$._audit").exists())
                .andExpect(jsonPath("$._audit.createdAt").exists())
                .andExpect(jsonPath("$._audit.createdBy").exists());
    }

    @Test
    void shouldCreateAccountSuccessfully_WithScalarClaimValues() throws Exception {
        // Given
        CreateAccountRequest request = new CreateAccountRequest();
        request.setAccountType(AccountType.WORKFORCE);
        request.setBusinessId(business.getId());
        request.setIdentityProviderId(identityProvider.getId());
        request.setIdentityId(identity.getId());
        request.setClaimValues(List.of(
                new ScalarClaimValueRequest(
                        workforceProfileClaimSet.getId(),
                        employeeIdClaimDef.getId(),
                        "EMP000011"
                ),
                new ScalarClaimValueRequest(
                        workforceProfileClaimSet.getId(),
                        departmentClaimDef.getId(),
                        "Engineering"
                )
        ));

        // When & Then
        mockMvc.perform(post("/api/v1/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.businessId").value(business.getId().toString()))
                .andExpect(jsonPath("$.identityId").value(identity.getId().toString()))
                .andExpect(jsonPath("$.accountType").value(AccountType.WORKFORCE.toString()));
    }

    @Test
    void shouldCreateAccountSuccessfully_WithArrayClaimValues() throws Exception {
        // Given
        CreateAccountRequest request = new CreateAccountRequest();
        request.setAccountType(AccountType.WORKFORCE);
        request.setBusinessId(business.getId());
        request.setIdentityProviderId(identityProvider.getId());
        request.setIdentityId(identity.getId());
        request.setClaimValues(List.of(
                new ScalarClaimValueRequest(
                        workforceProfileClaimSet.getId(),
                        employeeIdClaimDef.getId(),
                        "EMP000012"
                ),
                new ArrayClaimValueRequest(
                        workforceProfileClaimSet.getId(),
                        phoneNumbersClaimDef.getId(),
                        List.of("+***********", "+***********"),
                        0
                )
        ));

        // When & Then
        mockMvc.perform(post("/api/v1/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.businessId").value(business.getId().toString()))
                .andExpect(jsonPath("$.identityId").value(identity.getId().toString()))
                .andExpect(jsonPath("$.accountType").value(AccountType.WORKFORCE.toString()));
    }

    @Test
    void shouldReturnBadRequest_WhenRequestBodyIsEmpty() throws Exception {
        // When & Then
        mockMvc.perform(post("/api/v1/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{}"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void shouldReturnBadRequest_WhenBusinessIdIsMissing() throws Exception {
        // Given
        CreateAccountRequest request = new CreateAccountRequest();
        request.setAccountType(AccountType.WORKFORCE);
        request.setIdentityProviderId(identityProvider.getId());
        request.setIdentityId(identity.getId());
        // businessId is missing

        // When & Then
        mockMvc.perform(post("/api/v1/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void shouldReturnBadRequest_WhenIdentityIdIsMissing() throws Exception {
        // Given
        CreateAccountRequest request = new CreateAccountRequest();
        request.setAccountType(AccountType.WORKFORCE);
        request.setBusinessId(business.getId());
        request.setIdentityProviderId(identityProvider.getId());
        // identityId is missing

        // When & Then
        mockMvc.perform(post("/api/v1/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void shouldReturnBadRequest_WhenIdentityProviderIdIsMissing() throws Exception {
        // Given
        CreateAccountRequest request = new CreateAccountRequest();
        request.setAccountType(AccountType.WORKFORCE);
        request.setBusinessId(business.getId());
        request.setIdentityId(identity.getId());
        // identityProviderId is missing

        // When & Then
        mockMvc.perform(post("/api/v1/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void shouldReturnBadRequest_WhenAccountTypeIsMissing() throws Exception {
        // Given
        CreateAccountRequest request = new CreateAccountRequest();
        request.setBusinessId(business.getId());
        request.setIdentityProviderId(identityProvider.getId());
        request.setIdentityId(identity.getId());
        // accountType is missing

        // When & Then
        mockMvc.perform(post("/api/v1/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void shouldReturnConflict_WhenDuplicateAccountExists() throws Exception {
        // Given - Create first account
        AccountJpaEntity existingAccount = new AccountJpaEntity(business, identity, identityProvider, AccountType.WORKFORCE);
        existingAccount.setCreatedBy("test");
        accountRepository.save(existingAccount);

        CreateAccountRequest request = new CreateAccountRequest();
        request.setAccountType(AccountType.WORKFORCE);
        request.setBusinessId(business.getId());
        request.setIdentityProviderId(identityProvider.getId());
        request.setIdentityId(identity.getId());

        // When & Then
        mockMvc.perform(post("/api/v1/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isConflict())
                .andExpect(jsonPath("$.code").value(4304))
                .andExpect(jsonPath("$.message").value("Account already exists for this identity, business, identityProvider and type"));
    }

    @Test
    void shouldReturnBadRequest_WhenClaimDefinitionNotFound() throws Exception {
        // Given
        UUID nonExistentClaimDefId = UUID.randomUUID();
        CreateAccountRequest request = new CreateAccountRequest();
        request.setAccountType(AccountType.WORKFORCE);
        request.setBusinessId(business.getId());
        request.setIdentityProviderId(identityProvider.getId());
        request.setIdentityId(identity.getId());
        request.setClaimValues(List.of(
                new ScalarClaimValueRequest(
                        workforceProfileClaimSet.getId(),
                        nonExistentClaimDefId,
                        "test-value"
                )
        ));

        // When & Then
        mockMvc.perform(post("/api/v1/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void shouldReturnBadRequest_WhenClaimSetNotFound() throws Exception {
        // Given
        UUID nonExistentClaimSetId = UUID.randomUUID();
        CreateAccountRequest request = new CreateAccountRequest();
        request.setAccountType(AccountType.WORKFORCE);
        request.setBusinessId(business.getId());
        request.setIdentityProviderId(identityProvider.getId());
        request.setIdentityId(identity.getId());
        request.setClaimValues(List.of(
                new ScalarClaimValueRequest(
                        nonExistentClaimSetId,
                        employeeIdClaimDef.getId(),
                        "EMP000013"
                )
        ));

        // When & Then
        mockMvc.perform(post("/api/v1/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void shouldReturnBadRequest_WhenClaimDefinitionNotAssociatedWithClaimSet() throws Exception {
        // Given - Create a claim definition that's not associated with our claim set
        ClaimDefinitionJpaEntity unassociatedClaimDef = new ClaimDefinitionJpaEntity(
                "unassociated_claim",
                "Unassociated Claim",
                "A claim not associated with any claim set",
                ClaimType.SYSTEM_DEFINED,
                DataTypeEnum.STRING,
                null
        );
        unassociatedClaimDef.setCreatedBy("test");
        unassociatedClaimDef = claimDefinitionRepo.save(unassociatedClaimDef);

        CreateAccountRequest request = new CreateAccountRequest();
        request.setAccountType(AccountType.WORKFORCE);
        request.setBusinessId(business.getId());
        request.setIdentityProviderId(identityProvider.getId());
        request.setIdentityId(identity.getId());
        request.setClaimValues(List.of(
                new ScalarClaimValueRequest(
                        workforceProfileClaimSet.getId(),
                        unassociatedClaimDef.getId(),
                        "test-value"
                )
        ));

        // When & Then
        mockMvc.perform(post("/api/v1/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void shouldReturnBadRequest_WhenArrayClaimValueHasInvalidPrimaryIndex() throws Exception {
        // Given
        CreateAccountRequest request = new CreateAccountRequest();
        request.setAccountType(AccountType.WORKFORCE);
        request.setBusinessId(business.getId());
        request.setIdentityProviderId(identityProvider.getId());
        request.setIdentityId(identity.getId());
        request.setClaimValues(List.of(
                new ArrayClaimValueRequest(
                        workforceProfileClaimSet.getId(),
                        phoneNumbersClaimDef.getId(),
                        List.of("+***********", "+***********"),
                        5 // Invalid primary index - out of bounds
                )
        ));

        // When & Then
        mockMvc.perform(post("/api/v1/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void shouldReturnBadRequest_WhenArrayClaimValueHasEmptyValuesWithInvalidPrimaryIndex() throws Exception {
        // Given - Empty array claim values with primaryIndex should return 400
        CreateAccountRequest request = new CreateAccountRequest();
        request.setAccountType(AccountType.WORKFORCE);
        request.setBusinessId(business.getId());
        request.setIdentityProviderId(identityProvider.getId());
        request.setIdentityId(identity.getId());
        request.setClaimValues(List.of(
                new ArrayClaimValueRequest(
                        workforceProfileClaimSet.getId(),
                        phoneNumbersClaimDef.getId(),
                        List.of(), // Empty values list 
                        0 // primaryIndex 0 is out of bounds for empty array
                )
        ));

        // When & Then - Should return 400 since primaryIndex is out of bounds for empty array
        mockMvc.perform(post("/api/v1/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void shouldCreateAccount_WhenClaimValuesArrayIsEmpty() throws Exception {
        // Given - Empty claimValues array should create account without claims
        CreateAccountRequest request = new CreateAccountRequest();
        request.setAccountType(AccountType.WORKFORCE);
        request.setBusinessId(business.getId());
        request.setIdentityProviderId(identityProvider.getId());
        request.setIdentityId(identity.getId());
        request.setClaimValues(List.of()); // Empty claim values array

        // When & Then - Should succeed and create account without claim values
        mockMvc.perform(post("/api/v1/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.accountType").value("WORKFORCE"));
    }

    @Test
    void shouldReturnBadRequest_WhenClaimValueViolatesDataFormat() throws Exception {
        // Given
        CreateAccountRequest request = new CreateAccountRequest();
        request.setAccountType(AccountType.WORKFORCE);
        request.setBusinessId(business.getId());
        request.setIdentityProviderId(identityProvider.getId());
        request.setIdentityId(identity.getId());
        request.setClaimValues(List.of(
                new ScalarClaimValueRequest(
                        workforceProfileClaimSet.getId(),
                        employeeIdClaimDef.getId(),
                        "INVALID_FORMAT" // Should match ^EMP[0-9]{6}$
                )
        ));

        // When & Then
        mockMvc.perform(post("/api/v1/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void shouldCreateAccountSuccessfully_WithMetadata() throws Exception {
        // Given
        CreateAccountRequest request = new CreateAccountRequest();
        request.setAccountType(AccountType.WORKFORCE);
        request.setBusinessId(business.getId());
        request.setIdentityProviderId(identityProvider.getId());
        request.setIdentityId(identity.getId());
        request.setMetadata(Map.of(
                "department", "Engineering",
                "team", "Backend",
                "level", "Senior"
        ));

        // When & Then
        mockMvc.perform(post("/api/v1/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.metadata.department").value("Engineering"))
                .andExpect(jsonPath("$.metadata.team").value("Backend"))
                .andExpect(jsonPath("$.metadata.level").value("Senior"));
    }

    @Test
    void shouldCreateAccountSuccessfully_WithDifferentAccountTypes() throws Exception {
        // Test CUSTOMER account type
        IdentityJpaEntity customerIdentity = new IdentityJpaEntity(IdentityType.PERSON, "<EMAIL>");
        customerIdentity.setCreatedBy("test");
        customerIdentity = identityRepository.save(customerIdentity);

        CreateAccountRequest request = new CreateAccountRequest();
        request.setAccountType(AccountType.CUSTOMER);
        request.setBusinessId(business.getId());
        request.setIdentityProviderId(identityProvider.getId());
        request.setIdentityId(customerIdentity.getId());

        // When & Then
        mockMvc.perform(post("/api/v1/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.accountType").value(AccountType.CUSTOMER.toString()));
    }

    @Test
    void shouldReturnBadRequest_WhenBusinessNotFound() throws Exception {
        // Given
        UUID nonExistentBusinessId = UUID.randomUUID();
        CreateAccountRequest request = new CreateAccountRequest();
        request.setAccountType(AccountType.WORKFORCE);
        request.setBusinessId(nonExistentBusinessId);
        request.setIdentityProviderId(identityProvider.getId());
        request.setIdentityId(identity.getId());

        // When & Then
        mockMvc.perform(post("/api/v1/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void shouldReturnBadRequest_WhenIdentityNotFound() throws Exception {
        // Given
        UUID nonExistentIdentityId = UUID.randomUUID();
        CreateAccountRequest request = new CreateAccountRequest();
        request.setAccountType(AccountType.WORKFORCE);
        request.setBusinessId(business.getId());
        request.setIdentityProviderId(identityProvider.getId());
        request.setIdentityId(nonExistentIdentityId);

        // When & Then
        mockMvc.perform(post("/api/v1/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void shouldReturnBadRequest_WhenIdentityProviderNotFound() throws Exception {
        // Given
        UUID nonExistentIdentityProviderId = UUID.randomUUID();
        CreateAccountRequest request = new CreateAccountRequest();
        request.setAccountType(AccountType.WORKFORCE);
        request.setBusinessId(business.getId());
        request.setIdentityProviderId(nonExistentIdentityProviderId);
        request.setIdentityId(identity.getId());

        // When & Then
        mockMvc.perform(post("/api/v1/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void shouldCreateMultipleAccountsForSameIdentity_WithDifferentBusinesses() throws Exception {
        // Given - Create second business
        Business secondBusiness = new Business()
                .setName("Second Business")
                .setBusinessType(BusinessTypeEnum.BUSINESS_UNIT);
        secondBusiness.setCreatedBy("test");
        secondBusiness = businessRepository.save(secondBusiness);

        // Create first account
        CreateAccountRequest firstRequest = new CreateAccountRequest();
        firstRequest.setAccountType(AccountType.WORKFORCE);
        firstRequest.setBusinessId(business.getId());
        firstRequest.setIdentityProviderId(identityProvider.getId());
        firstRequest.setIdentityId(identity.getId());

        mockMvc.perform(post("/api/v1/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(firstRequest)))
                .andExpect(status().isCreated());

        // Create second account for same identity but different business
        CreateAccountRequest secondRequest = new CreateAccountRequest();
        secondRequest.setAccountType(AccountType.WORKFORCE);
        secondRequest.setBusinessId(secondBusiness.getId());
        secondRequest.setIdentityProviderId(identityProvider.getId());
        secondRequest.setIdentityId(identity.getId());

        // When & Then
        mockMvc.perform(post("/api/v1/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(secondRequest)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.businessId").value(secondBusiness.getId().toString()));
    }

    @Test
    void shouldCreateMultipleAccountsForSameIdentity_WithDifferentAccountTypes() throws Exception {
        // Create first account with WORKFORCE type
        CreateAccountRequest firstRequest = new CreateAccountRequest();
        firstRequest.setAccountType(AccountType.WORKFORCE);
        firstRequest.setBusinessId(business.getId());
        firstRequest.setIdentityProviderId(identityProvider.getId());
        firstRequest.setIdentityId(identity.getId());

        mockMvc.perform(post("/api/v1/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(firstRequest)))
                .andExpect(status().isCreated());

        // Create second account for same identity but different account type
        CreateAccountRequest secondRequest = new CreateAccountRequest();
        secondRequest.setAccountType(AccountType.CUSTOMER);
        secondRequest.setBusinessId(business.getId());
        secondRequest.setIdentityProviderId(identityProvider.getId());
        secondRequest.setIdentityId(identity.getId());

        // When & Then
        mockMvc.perform(post("/api/v1/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(secondRequest)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.accountType").value(AccountType.CUSTOMER.toString()));
    }

    // ========================================
    // GET /accounts Tests
    // ========================================

    @Test
    void shouldGetAllAccounts_WithPagination() throws Exception {
        // Given - Create multiple accounts
        AccountJpaEntity account1 = new AccountJpaEntity(business, identity, identityProvider, AccountType.WORKFORCE);
        account1.setCreatedBy("test");
        accountRepository.save(account1);

        IdentityJpaEntity identity2 = new IdentityJpaEntity(IdentityType.PERSON, "<EMAIL>");
        identity2.setCreatedBy("test");
        identity2 = identityRepository.save(identity2);

        AccountJpaEntity account2 = new AccountJpaEntity(business, identity2, identityProvider, AccountType.CUSTOMER);
        account2.setCreatedBy("test");
        accountRepository.save(account2);

        // When & Then
        mockMvc.perform(get("/api/v1/accounts")
                        .param("currentPage", "1")
                        .param("pageSize", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.currentPage").value(1))
                .andExpect(jsonPath("$.pageSize").value(10))
                .andExpect(jsonPath("$.totalElements").value(greaterThanOrEqualTo(2)))
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content", hasSize(greaterThanOrEqualTo(2))))
                .andExpect(jsonPath("$.content[0].id").exists())
                .andExpect(jsonPath("$.content[0].businessId").exists())
                .andExpect(jsonPath("$.content[0].accountType").exists());
    }

    @Test
    void shouldGetAllAccounts_WithDefaultPagination() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/accounts"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.currentPage").value(1))
                .andExpect(jsonPath("$.pageSize").value(10))
                .andExpect(jsonPath("$.content").isArray());
    }

    // ========================================
    // GET /accounts/{accountId} Tests
    // ========================================

    @Test
    void shouldGetAccountById_Successfully() throws Exception {
        // Given
        AccountJpaEntity account = new AccountJpaEntity(business, identity, identityProvider, AccountType.WORKFORCE);
        account.setMetadata(Map.of("department", "Engineering", "level", "Senior"));
        account.setCreatedBy("test");
        account = accountRepository.save(account);

        // When & Then
        mockMvc.perform(get("/api/v1/accounts/{accountId}", account.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(account.getId().toString()))
                .andExpect(jsonPath("$.businessId").value(business.getId().toString()))
                .andExpect(jsonPath("$.identityId").value(identity.getId().toString()))
                .andExpect(jsonPath("$.identityProviderId").value(identityProvider.getId().toString()))
                .andExpect(jsonPath("$.accountType").value(AccountType.WORKFORCE.toString()))
                .andExpect(jsonPath("$.metadata.department").value("Engineering"))
                .andExpect(jsonPath("$.metadata.level").value("Senior"))
                .andExpect(jsonPath("$._audit").exists());
    }

    @Test
    void shouldReturnNotFound_WhenAccountIdDoesNotExist() throws Exception {
        // Given
        UUID nonExistentAccountId = UUID.randomUUID();

        // When & Then
        mockMvc.perform(get("/api/v1/accounts/{accountId}", nonExistentAccountId))
                .andExpect(status().isNotFound());
    }

    @Test
    void shouldReturnBadRequest_WhenAccountIdIsInvalid() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/accounts/{accountId}", "invalid-uuid"))
                .andExpect(status().isBadRequest());
    }

    // ========================================
    // PATCH /accounts/{accountId} Tests
    // ========================================

    @Test
    void shouldUpdateAccount_Successfully() throws Exception {
        // Given
        AccountJpaEntity account = new AccountJpaEntity(business, identity, identityProvider, AccountType.WORKFORCE);
        account.setCreatedBy("test");
        account = accountRepository.save(account);

        UpdateAccountRequest updateRequest = new UpdateAccountRequest();
        updateRequest.setLifecycleState(AccountLifecycleState.ACTIVE);
        updateRequest.setMetadata(Map.of("department", "Engineering", "updated", "true"));

        // When & Then
        mockMvc.perform(patch("/api/v1/accounts/{accountId}", account.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isNoContent());

        // Verify the account was updated
        AccountJpaEntity updatedAccount = accountRepository.findById(account.getId()).orElseThrow();
        assertEquals(AccountLifecycleState.ACTIVE, updatedAccount.getLifecycleState());
        assertEquals("Engineering", updatedAccount.getMetadata().get("department"));
        assertEquals("true", updatedAccount.getMetadata().get("updated"));
    }

    @Test
    void shouldUpdateAccount_WithOnlyLifecycleState() throws Exception {
        // Given
        AccountJpaEntity account = new AccountJpaEntity(business, identity, identityProvider, AccountType.WORKFORCE);
        account.setCreatedBy("test");
        account = accountRepository.save(account);

        UpdateAccountRequest updateRequest = new UpdateAccountRequest();
        updateRequest.setLifecycleState(AccountLifecycleState.SUSPENDED);

        // When & Then
        mockMvc.perform(patch("/api/v1/accounts/{accountId}", account.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isNoContent());

        // Verify the account was updated
        AccountJpaEntity updatedAccount = accountRepository.findById(account.getId()).orElseThrow();
        assertEquals(AccountLifecycleState.SUSPENDED, updatedAccount.getLifecycleState());
    }

    @Test
    void shouldUpdateAccount_WithOnlyMetadata() throws Exception {
        // Given
        AccountJpaEntity account = new AccountJpaEntity(business, identity, identityProvider, AccountType.WORKFORCE);
        account.setCreatedBy("test");
        account = accountRepository.save(account);

        UpdateAccountRequest updateRequest = new UpdateAccountRequest();
        updateRequest.setMetadata(Map.of("newField", "newValue", "department", "Sales"));

        // When & Then
        mockMvc.perform(patch("/api/v1/accounts/{accountId}", account.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isNoContent());

        // Verify the account was updated
        AccountJpaEntity updatedAccount = accountRepository.findById(account.getId()).orElseThrow();
        assertEquals("newValue", updatedAccount.getMetadata().get("newField"));
        assertEquals("Sales", updatedAccount.getMetadata().get("department"));
    }

    @Test
    void shouldReturnNotFound_WhenUpdatingNonExistentAccount() throws Exception {
        // Given
        UUID nonExistentAccountId = UUID.randomUUID();
        UpdateAccountRequest updateRequest = new UpdateAccountRequest();
        updateRequest.setLifecycleState(AccountLifecycleState.ACTIVE);

        // When & Then
        mockMvc.perform(patch("/api/v1/accounts/{accountId}", nonExistentAccountId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isNotFound());
    }

    // ========================================
    // GET /accounts/identity/{identityId} Tests
    // ========================================

    @Test
    void shouldGetAccountsByIdentity_Successfully() throws Exception {
        // Given - Create multiple accounts for the same identity
        AccountJpaEntity account1 = new AccountJpaEntity(business, identity, identityProvider, AccountType.WORKFORCE);
        account1.setCreatedBy("test");
        accountRepository.save(account1);

        // Create second business
        Business secondBusiness = new Business()
                .setName("Second Business")
                .setBusinessType(BusinessTypeEnum.BUSINESS_UNIT);
        secondBusiness.setCreatedBy("test");
        secondBusiness = businessRepository.save(secondBusiness);

        AccountJpaEntity account2 = new AccountJpaEntity(secondBusiness, identity, identityProvider, AccountType.CUSTOMER);
        account2.setCreatedBy("test");
        accountRepository.save(account2);

        // When & Then
        mockMvc.perform(get("/api/v1/accounts/identity/{identityId}", identity.getId())
                        .param("page", "1")
                        .param("pageSize", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.currentPage").value(1))
                .andExpect(jsonPath("$.pageSize").value(10))
                .andExpect(jsonPath("$.totalElements").value(2))
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content", hasSize(2)))
                .andExpect(jsonPath("$.content[0].identityId").value(identity.getId().toString()))
                .andExpect(jsonPath("$.content[1].identityId").value(identity.getId().toString()));
    }

    @Test
    void shouldGetAccountsByIdentity_WithDefaultPagination() throws Exception {
        // Given
        AccountJpaEntity account = new AccountJpaEntity(business, identity, identityProvider, AccountType.WORKFORCE);
        account.setCreatedBy("test");
        accountRepository.save(account);

        // When & Then
        mockMvc.perform(get("/api/v1/accounts/identity/{identityId}", identity.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.currentPage").value(1))
                .andExpect(jsonPath("$.pageSize").value(10))
                .andExpect(jsonPath("$.content").isArray());
    }

    @Test
    void shouldReturnEmptyList_WhenIdentityHasNoAccounts() throws Exception {
        // Given
        IdentityJpaEntity identityWithoutAccounts = new IdentityJpaEntity(IdentityType.PERSON, "<EMAIL>");
        identityWithoutAccounts.setCreatedBy("test");
        identityWithoutAccounts = identityRepository.save(identityWithoutAccounts);

        // When & Then
        mockMvc.perform(get("/api/v1/accounts/identity/{identityId}", identityWithoutAccounts.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.totalElements").value(0))
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content", hasSize(0)));
    }

    // ========================================
    // POST /businesses/{businessId}/accounts/identify Tests
    // ========================================

    @Test
    void shouldIdentifyAccount_ByClaimValues_Successfully() throws Exception {
        // Given - Create account with claim values
        AccountJpaEntity account = new AccountJpaEntity(business, identity, identityProvider, AccountType.WORKFORCE);
        account.setCreatedBy("test");
        account = accountRepository.save(account);

        // Create claim value for identification
        ClaimValueJpaEntity claimValue = new ClaimValueJpaEntity(
                employeeIdClaimDef,
                OwnerType.ACCOUNT,
                account.getId()
        );
        claimValue.setValue("EMP000123");
        claimValue.setPrimary(true);
        claimValue.setComputed(false);
        claimValue.setSource("USER_INPUT");
        claimValue.setCreatedBy("test");
        claimValueRepo.save(claimValue);

        // Create claim set claim value relationship
        ClaimSetClaimValueJpaEntity claimSetClaimValue = new ClaimSetClaimValueJpaEntity(
                workforceIdentifierClaimSet,
                claimValue
        );
        claimSetClaimValue.setCreatedBy("test");
        claimValue.setClaimSetClaimValue(claimSetClaimValue);

        List<ClaimIdentifierRequest> claimIdentifiers = List.of(
                new ClaimIdentifierRequest() {{
                    setClaimDefinitionId(employeeIdClaimDef.getId());
                    setValue("EMP000123");
                }}
        );

        // When & Then
        mockMvc.perform(post("/api/v1/businesses/{businessId}/accounts/identify", business.getId())
                        .param("accountType", AccountType.WORKFORCE.toString())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(claimIdentifiers)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(account.getId().toString()))
                .andExpect(jsonPath("$.businessId").value(business.getId().toString()))
                .andExpect(jsonPath("$.identityId").value(identity.getId().toString()))
                .andExpect(jsonPath("$.accountType").value(AccountType.WORKFORCE.toString()));
    }

    @Test
    void shouldReturnNotFound_WhenNoAccountMatchesClaims() throws Exception {
        // Given
        List<ClaimIdentifierRequest> claimIdentifiers = List.of(
                new ClaimIdentifierRequest() {{
                    setClaimDefinitionId(employeeIdClaimDef.getId());
                    setValue("NONEXISTENT123");
                }}
        );

        // When & Then
        mockMvc.perform(post("/api/v1/businesses/{businessId}/accounts/identify", business.getId())
                        .param("accountType", AccountType.WORKFORCE.toString())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(claimIdentifiers)))
                .andExpect(status().isNotFound());
    }

    @Test
    void shouldReturnBadRequest_WhenClaimIdentifiersListIsEmpty() throws Exception {
        // When & Then
        mockMvc.perform(post("/api/v1/businesses/{businessId}/accounts/identify", business.getId())
                        .param("accountType", AccountType.WORKFORCE.toString())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("[]"))
                .andExpect(status().isBadRequest());
    }

    @Test
    void shouldReturnBadRequest_WhenAccountTypeParameterIsMissing() throws Exception {
        // Given
        List<ClaimIdentifierRequest> claimIdentifiers = List.of(
                new ClaimIdentifierRequest() {{
                    setClaimDefinitionId(employeeIdClaimDef.getId());
                    setValue("EMP000123");
                }}
        );

        // When & Then
        mockMvc.perform(post("/api/v1/businesses/{businessId}/accounts/identify", business.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(claimIdentifiers)))
                .andExpect(status().isBadRequest());
    }

    // ========================================
    // GET /accounts/{accountId}/claims Tests
    // ========================================

    @Test
    void shouldGetAccountWithClaims_Successfully() throws Exception {
        // Given - Create account with claim values
        AccountJpaEntity account = new AccountJpaEntity(business, identity, identityProvider, AccountType.WORKFORCE);
        account.setCreatedBy("test");
        account = accountRepository.save(account);

        // Create scalar claim value
        ClaimValueJpaEntity scalarClaimValue = new ClaimValueJpaEntity(
                employeeIdClaimDef,
                OwnerType.ACCOUNT,
                account.getId()
        );
        scalarClaimValue.setValue("EMP000456");
        scalarClaimValue.setPrimary(true);
        scalarClaimValue.setComputed(false);
        scalarClaimValue.setSource("USER_INPUT");
        scalarClaimValue.setCreatedBy("test");
        claimValueRepo.save(scalarClaimValue);

        // Create claim set claim value relationship for scalar
        ClaimSetClaimValueJpaEntity scalarClaimSetClaimValue = new ClaimSetClaimValueJpaEntity(
                workforceProfileClaimSet,
                scalarClaimValue
        );
        scalarClaimSetClaimValue.setCreatedBy("test");
        scalarClaimValue.setClaimSetClaimValue(scalarClaimSetClaimValue);

        // Create array claim value
        ClaimValueJpaEntity arrayClaimValue = new ClaimValueJpaEntity(
                phoneNumbersClaimDef,
                OwnerType.ACCOUNT,
                account.getId()
        );
        arrayClaimValue.setValue("[\"" + String.join("\",\"", "+***********", "+***********") + "\"]");
        arrayClaimValue.setPrimary(true);
        arrayClaimValue.setComputed(false);
        arrayClaimValue.setSource("USER_INPUT");
        arrayClaimValue.setCreatedBy("test");
        claimValueRepo.save(arrayClaimValue);

        // Create claim set claim value relationship for array
        ClaimSetClaimValueJpaEntity arrayClaimSetClaimValue = new ClaimSetClaimValueJpaEntity(
                workforceProfileClaimSet,
                arrayClaimValue
        );
        arrayClaimSetClaimValue.setCreatedBy("test");
        arrayClaimValue.setClaimSetClaimValue(arrayClaimSetClaimValue);

        // When & Then
        mockMvc.perform(get("/api/v1/accounts/{accountId}/claims", account.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(account.getId().toString()))
                .andExpect(jsonPath("$.businessId").value(business.getId().toString()))
                .andExpect(jsonPath("$.identityId").value(identity.getId().toString()))
                .andExpect(jsonPath("$.accountType").value(AccountType.WORKFORCE.toString()))
                .andExpect(jsonPath("$.claimValues").isArray())
                .andExpect(jsonPath("$.claimValues", hasSize(2)))
                .andExpect(jsonPath("$.claimValues[0].claimDefinitionId").exists())
                .andExpect(jsonPath("$.claimValues[0].ownerType").value(OwnerType.ACCOUNT.toString()))
                .andExpect(jsonPath("$.claimValues[0].ownerId").value(account.getId().toString()));
    }

    @Test
    void shouldGetAccountWithClaims_WhenAccountHasNoClaims() throws Exception {
        // Given - Create account without claim values
        AccountJpaEntity account = new AccountJpaEntity(business, identity, identityProvider, AccountType.WORKFORCE);
        account.setCreatedBy("test");
        account = accountRepository.save(account);

        // When & Then
        mockMvc.perform(get("/api/v1/accounts/{accountId}/claims", account.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(account.getId().toString()))
                .andExpect(jsonPath("$.businessId").value(business.getId().toString()))
                .andExpect(jsonPath("$.claimValues").isArray())
                .andExpect(jsonPath("$.claimValues", hasSize(0)));
    }

    @Test
    void shouldReturnNotFound_WhenGettingClaimsForNonExistentAccount() throws Exception {
        // Given
        UUID nonExistentAccountId = UUID.randomUUID();

        // When & Then
        mockMvc.perform(get("/api/v1/accounts/{accountId}/claims", nonExistentAccountId))
                .andExpect(status().isNotFound());
    }

    @Test
    void shouldReturnBadRequest_WhenGettingClaimsWithInvalidAccountId() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/accounts/{accountId}/claims", "invalid-uuid"))
                .andExpect(status().isBadRequest());
    }

    // ========================================
    // Edge Cases and Integration Tests
    // ========================================

    @Test
    void shouldHandleComplexScenario_CreateAccountThenRetrieveWithClaims() throws Exception {
        // Given - Create account with claim values via POST
        CreateAccountRequest createRequest = new CreateAccountRequest();
        createRequest.setAccountType(AccountType.WORKFORCE);
        createRequest.setBusinessId(business.getId());
        createRequest.setIdentityProviderId(identityProvider.getId());
        createRequest.setIdentityId(identity.getId());
        createRequest.setMetadata(Map.of("department", "Engineering"));
        createRequest.setClaimValues(List.of(
                new ScalarClaimValueRequest(
                        workforceProfileClaimSet.getId(),
                        employeeIdClaimDef.getId(),
                        "EMP000789"
                )
        ));

        // Create account
        String createResponse = mockMvc.perform(post("/api/v1/accounts")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString();

        // Extract account ID from response
        String accountId = objectMapper.readTree(createResponse).get("id").asText();

        // Then retrieve account with claims
        mockMvc.perform(get("/api/v1/accounts/{accountId}/claims", accountId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(accountId))
                .andExpect(jsonPath("$.metadata.department").value("Engineering"))
                .andExpect(jsonPath("$.claimValues").isArray())
                .andExpect(jsonPath("$.claimValues", hasSize(1)))
                .andExpect(jsonPath("$.claimValues[0].value").value("EMP000789"));

        // Then identify account by claims
        List<ClaimIdentifierRequest> claimIdentifiers = List.of(
                new ClaimIdentifierRequest() {{
                    setClaimDefinitionId(employeeIdClaimDef.getId());
                    setValue("EMP000789");
                }}
        );

        mockMvc.perform(post("/api/v1/businesses/{businessId}/accounts/identify", business.getId())
                        .param("accountType", AccountType.WORKFORCE.toString())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(claimIdentifiers)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(accountId));
    }

    /**
     * Cleanup method to remove test data created specifically for these tests.
     * Note: BaseIntegrationTest already provides database cleanup between tests,
     * but this method demonstrates explicit cleanup for reference.
     */
    private void cleanupTestData() {
        // Clean up claim values
        claimValueRepo.deleteAll();

        // Clean up claim set definition mappings
        mappingRepo.deleteAll();

        // Clean up claim sets
        claimSetRepo.deleteAll();

        // Clean up claim definitions
        claimDefinitionRepo.deleteAll();

        // Clean up accounts
        accountRepository.deleteAll();

        // Clean up identities
        identityRepository.deleteAll();

        // Clean up businesses (except system business)
        businessRepository.findAll().stream()
                .filter(b -> !b.getId().equals(DataSeedConstants.SYSTEM_BUSINESS_ID))
                .forEach(businessRepository::delete);
    }
}
