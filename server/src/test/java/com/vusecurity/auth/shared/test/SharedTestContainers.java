package com.vusecurity.auth.shared.test;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.testcontainers.containers.MSSQLServerContainer;
import org.testcontainers.containers.wait.strategy.Wait;

/**
 * Shared TestContainers configuration for all integration tests.
 * This class provides a single SQL Server container instance that is reused
 * across all test classes to improve performance and reduce resource usage.
 */
public class SharedTestContainers {
    
    private static final Logger logger = LoggerFactory.getLogger(SharedTestContainers.class);
    
    // Single shared container instance for all tests
    private static final MSSQLServerContainer<?> SQL_SERVER_CONTAINER = createContainer();
    
    /**
     * Creates and configures the shared SQL Server container
     */
    private static MSSQLServerContainer<?> createContainer() {
        logger.info("Creating shared SQL Server container for integration tests");
        
        return new MSSQLServerContainer<>("mcr.microsoft.com/mssql/server:2022-latest")
                .acceptLicense()
                .withReuse(true)
                .waitingFor(Wait.forLogMessage(".*Recovery is complete.*\\n", 1));
    }
    
    /**
     * Gets the shared SQL Server container instance
     */
    public static MSSQLServerContainer<?> getSqlServerContainer() {
        return SQL_SERVER_CONTAINER;
    }
    
    /**
     * Starts the shared container if not already started
     */
    public static void startContainer() {
        if (!SQL_SERVER_CONTAINER.isRunning()) {
            logger.info("Starting shared SQL Server container");
            SQL_SERVER_CONTAINER.start();
            logger.info("Shared SQL Server container started: {}", SQL_SERVER_CONTAINER.getJdbcUrl());
        }
    }
    
    /**
     * Configures Spring properties for the shared container
     */
    public static void configureProperties(DynamicPropertyRegistry registry) {
        startContainer();
        
        // Configure TestContainers datasource
        registry.add("spring.datasource.url", SQL_SERVER_CONTAINER::getJdbcUrl);
        registry.add("spring.datasource.username", SQL_SERVER_CONTAINER::getUsername);
        registry.add("spring.datasource.password", SQL_SERVER_CONTAINER::getPassword);
        registry.add("spring.datasource.driver-class-name", SQL_SERVER_CONTAINER::getDriverClassName);
        
        // Optimize connection pool for tests
        registry.add("spring.datasource.hikari.maximum-pool-size", () -> "5");
        registry.add("spring.datasource.hikari.minimum-idle", () -> "1");
        registry.add("spring.datasource.hikari.connection-timeout", () -> "20000");
        registry.add("spring.datasource.hikari.idle-timeout", () -> "300000");
        registry.add("spring.datasource.hikari.leak-detection-threshold", () -> "30000");
        
        // Required multitenant environment variables for testing
        registry.add("API_KEY", () -> "test-api-key");
        registry.add("MULTITENANT_KEY", () -> "test-multitenant-key");
        registry.add("MULTITENANT_URL", () -> "http://localhost:3000");
        registry.add("MULTITENANT_TRANSIT_KEY", () -> "test-transit-key");
        registry.add("MULTITENANT_ID_HEADER", () -> "X-ORIGINAL-HOST");
        registry.add("MULTITENANT_JPA_MODE", () -> "full");
        
        // App properties
        registry.add("app.apikey", () -> "test-api-key");
        registry.add("app.multitenant.key", () -> "test-multitenant-key");
        registry.add("app.multitenant.url", () -> "http://localhost:3000");
        registry.add("app.multitenant.transitKey", () -> "test-transit-key");
        registry.add("app.multitenant.idHeader", () -> "X-ORIGINAL-HOST");
        registry.add("app.multitenant.applicationName", () -> "auth-server");
        registry.add("app.multitenant.jpa.mode", () -> "full");
        registry.add("app.multitenant.test.single-datasource", () -> "true");
        registry.add("app.version", () -> "1.0.0-SNAPSHOT");
        registry.add("app.build", () -> "test-build");
        registry.add("app.date", () -> "1640995200000");
        
        // Enable data seeding for tests
        registry.add("app.dataseed.enabled", () -> "true");
        registry.add("ENABLE_DATA_SEEDING", () -> "true");
        
        // Hibernate configuration for tests
        registry.add("hibernate.hbm2ddl.auto", () -> "create-drop");
        registry.add("hibernate.show_sql", () -> "false");
        registry.add("hibernate.dialect", () -> "org.hibernate.dialect.SQLServerDialect");
        registry.add("hibernate.implicit_naming_strategy", () -> "org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl");
        registry.add("hibernate.physical_naming_strategy", () -> "org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy");
        
        // Logging configuration
        registry.add("logging.level.root", () -> "WARN");
        registry.add("logging.level.com.vusecurity", () -> "INFO");
        registry.add("logging.level.org.testcontainers", () -> "INFO");
        registry.add("logging.level.org.springframework.boot.test", () -> "INFO");
        registry.add("logging.level.org.hibernate.SQL", () -> "WARN");
        registry.add("logging.level.org.hibernate.type.descriptor.sql.BasicBinder", () -> "WARN");
        
        logger.info("Configured shared container properties for: {}", SQL_SERVER_CONTAINER.getJdbcUrl());
    }
    
    /**
     * Enables container logging for debugging
     */
    public static void enableContainerLogging() {
        try {
            // Ensure container is started before enabling logging
            startContainer();
            
            if (SQL_SERVER_CONTAINER.isRunning()) {
                SQL_SERVER_CONTAINER.followOutput(consumer -> {
                    String logLine = consumer.getUtf8String().trim();
                    if (!logLine.isEmpty()) {
                        logger.debug("SQL Server Container: {}", logLine);
                    }
                });
                logger.debug("Container logging enabled for: {}", SQL_SERVER_CONTAINER.getContainerId());
            } else {
                logger.warn("Cannot enable container logging - container is not running");
            }
        } catch (Exception e) {
            logger.warn("Failed to enable container logging: {}", e.getMessage());
        }
    }
}