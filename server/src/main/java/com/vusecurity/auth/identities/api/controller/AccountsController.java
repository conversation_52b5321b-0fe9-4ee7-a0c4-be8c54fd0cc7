package com.vusecurity.auth.identities.api.controller;

import static com.vusecurity.auth.authorization.domain.PermissionNames.ACCOUNT_CREATE;
import static com.vusecurity.auth.authorization.domain.PermissionNames.ACCOUNT_READ;
import static com.vusecurity.auth.authorization.domain.PermissionNames.ACCOUNT_UPDATE;

import java.util.List;
import java.util.NoSuchElementException;
import java.util.UUID;

import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ResponseStatusException;

import com.vusecurity.auth.claims.application.query.GetClaimValueQuery;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.claims.mapper.ClaimValueDtoMapper;
import com.vusecurity.auth.contracts.api.v1.dto.identities.AccountResponse;
import com.vusecurity.auth.contracts.api.v1.dto.identities.AccountWithClaimsResponse;
import com.vusecurity.auth.contracts.api.v1.dto.identities.ClaimIdentifierRequest;
import com.vusecurity.auth.contracts.api.v1.dto.identities.CreateAccountRequest;
import com.vusecurity.auth.contracts.api.v1.dto.identities.UpdateAccountRequest;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.OwnerType;
import com.vusecurity.auth.identities.application.command.CreateAccountCommand;
import com.vusecurity.auth.identities.application.command.UpdateAccountCommand;
import com.vusecurity.auth.identities.application.handler.CreateAccountWithClaimValuesHandler;
import com.vusecurity.auth.identities.application.handler.UpdateAccountHandler;
import com.vusecurity.auth.identities.application.query.GetAccountQuery;
import com.vusecurity.auth.identities.application.query.GetAccountsByIdentityQuery;
import com.vusecurity.auth.identities.application.query.GetAccountsPagedQuery;
import com.vusecurity.auth.identities.application.query.IdentifyAccountByClaimsQuery;
import com.vusecurity.auth.identities.application.query.IdentifyAccountQuery;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.mapper.AccountDtoMapper;
import com.vusecurity.auth.shared.config.aspect.Oauth2Authorize;
import com.vusecurity.core.commons.models.PageableResponse;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;

@RestController
@SecurityRequirement(name = "oauth2 client credentials")
@Tag(name = "Accounts", description = "Account management operations")
@RequestMapping("${app.authorization.context-path}")
public class AccountsController {
        private final CreateAccountWithClaimValuesHandler createAccountWithClaimValuesHandler;
        private final GetAccountQuery getAccountQuery;
        private final IdentifyAccountQuery identifyAccountQuery;
        private final UpdateAccountHandler updateAccountHandler;
        private final AccountDtoMapper accountDtoMapper;
        private final GetClaimValueQuery getClaimValueQuery;
        private final ClaimValueDtoMapper claimValueDtoMapper;

        public AccountsController(
                        CreateAccountWithClaimValuesHandler createAccountWithClaimValuesHandler,
                        GetAccountQuery getAccountQuery,
                        IdentifyAccountQuery identifyAccountQuery,
                        UpdateAccountHandler updateAccountHandler,
                        AccountDtoMapper accountDtoMapper,
                        GetClaimValueQuery getClaimValueQuery,
                        ClaimValueDtoMapper claimValueDtoMapper) {
                this.createAccountWithClaimValuesHandler = createAccountWithClaimValuesHandler;
                this.getAccountQuery = getAccountQuery;
                this.identifyAccountQuery = identifyAccountQuery;
                this.updateAccountHandler = updateAccountHandler;
                this.accountDtoMapper = accountDtoMapper;
                this.getClaimValueQuery = getClaimValueQuery;
                this.claimValueDtoMapper = claimValueDtoMapper;
        }

        @Operation(summary = "Get all accounts", description = "Retrieves a paginated list of accounts with optional filtering. Supports filtering by various account properties.")
        @ApiResponses({
                        @ApiResponse(responseCode = "200", description = "Successful retrieval", content = @Content(schema = @Schema(implementation = PageableResponse.class))),
                        @ApiResponse(responseCode = "400", description = "Invalid request parameters"),
                        @ApiResponse(responseCode = "401", description = "Authentication required"),
                        @ApiResponse(responseCode = "403", description = "Access denied")
        })
        @GetMapping("/accounts")
        @Oauth2Authorize(permission = ACCOUNT_READ)
        public PageableResponse<AccountResponse> get(@RequestParam(name = "page", defaultValue = "1") int page,
                        @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                        @RequestParam(name = "filter", required = false) String filter) {

                GetAccountsPagedQuery query = GetAccountsPagedQuery.builder()
                                .page(page)
                                .pageSize(pageSize)
                                .filter(filter)
                                .build();

                Page<AccountJpaEntity> accounts = getAccountQuery.getAllAccounts(query);
                return new PageableResponse<>(accounts.getNumber() + 1, accounts.getSize(), accounts.getTotalElements(),
                                accounts.get().map(accountDtoMapper::toResponse).toList());
        }

        @Operation(summary = "Create a new account", description = "Creates a new account associated with a specific identity and business. All required fields must be provided including businessId, identityId, identityProviderId, and accountType.")
        @ApiResponses({
                        @ApiResponse(responseCode = "201", description = "Account created successfully", content = @Content(schema = @Schema(implementation = AccountResponse.class))),
                        @ApiResponse(responseCode = "400", description = "Invalid request data or validation errors"),
                        @ApiResponse(responseCode = "401", description = "Authentication required"),
                        @ApiResponse(responseCode = "403", description = "Access denied"),
                        @ApiResponse(responseCode = "409", description = "Account with same properties already exists")
        })
        @PostMapping("/accounts")
        @ResponseStatus(HttpStatus.CREATED)
        @Oauth2Authorize(permission = ACCOUNT_CREATE)
        public AccountResponse create(@RequestBody @Valid CreateAccountRequest request) {

                CreateAccountCommand cmd = new CreateAccountCommand(
                                request.getIdentityId(),
                                request.getBusinessId(),
                                request.getIdentityProviderId(),
                                request.getAccountType(),
                                request.getLifecycleState(),
                                request.getMetadata(),
                                request.getClaimValues());

                AccountJpaEntity entity = createAccountWithClaimValuesHandler.handle(cmd);
                return accountDtoMapper.toResponse(entity);
        }

        @Operation(summary = "Get account by ID", description = "Retrieves a specific account by its unique identifier.")
        @ApiResponses({
                        @ApiResponse(responseCode = "200", description = "Account found", content = @Content(schema = @Schema(implementation = AccountResponse.class))),
                        @ApiResponse(responseCode = "404", description = "Account not found"),
                        @ApiResponse(responseCode = "401", description = "Authentication required"),
                        @ApiResponse(responseCode = "403", description = "Access denied")
        })
        @GetMapping("/accounts/{accountId}")
        @Oauth2Authorize(permission = ACCOUNT_READ)
        public AccountResponse get(@PathVariable(value = "accountId") String accountId) {

                AccountJpaEntity account = getAccountQuery.getAccountById(accountId);
                return accountDtoMapper.toResponse(account);
        }

        @Operation(summary = "Partially update an account", description = "Allows you to update one or more attributes of an account by its accountId")
        @ApiResponses({
                        @ApiResponse(responseCode = "204", description = "Account updated successfully"),
                        @ApiResponse(responseCode = "404", description = "Account not found"),
                        @ApiResponse(responseCode = "401", description = "Client is not authenticated or the credentials provided are incorrect"),
                        @ApiResponse(responseCode = "403", description = "Client authenticated, but does not have permission to access the resource, even if it has valid credentials")
        })
        @PatchMapping(path = "/accounts/{accountId}")
        @ResponseStatus(HttpStatus.NO_CONTENT)
        @Oauth2Authorize(permission = ACCOUNT_UPDATE)
        public void update(@PathVariable(value = "accountId") UUID accountId,
                        @RequestBody @Valid UpdateAccountRequest request) {

                UpdateAccountCommand command = new UpdateAccountCommand(
                                accountId,
                                request.getLifecycleState(),
                                request.getMetadata());

                updateAccountHandler.updateAccount(command);
        }

        @Operation(summary = "Get accounts by identity ID", description = "Retrieves a paginated list of accounts associated with a specific identity.")
        @ApiResponses({
                        @ApiResponse(responseCode = "200", description = "Accounts found", content = @Content(schema = @Schema(implementation = PageableResponse.class))),
                        @ApiResponse(responseCode = "404", description = "Identity not found"),
                        @ApiResponse(responseCode = "401", description = "Authentication required"),
                        @ApiResponse(responseCode = "403", description = "Access denied")
        })
        @GetMapping("/accounts/identity/{identityId}")
        @Oauth2Authorize(permission = ACCOUNT_READ)
        public PageableResponse<AccountResponse> getAllByIdentityUuid(
                        @RequestParam(name = "page", defaultValue = "1") int page,
                        @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
                        @PathVariable(value = "identityId") UUID identityId) {

                GetAccountsByIdentityQuery query = GetAccountsByIdentityQuery.builder()
                                .identityId(identityId)
                                .page(page)
                                .pageSize(pageSize)
                                .build();

                Page<AccountJpaEntity> accounts = getAccountQuery.getAccountsByIdentity(query);
                return new PageableResponse<>(accounts.getNumber() + 1, accounts.getSize(), accounts.getTotalElements(),
                                accounts.get().map(accountDtoMapper::toResponse).toList());
        }

        @Operation(summary = "Identify account by claims", description = "Identifies exactly one account for a business using claim-based matching. "
                        +
                        "Requires a business ID, account type, and a list of claim identifiers. " +
                        "Uses ALL_CLAIMS_MUST_MATCH strategy to find the account that matches all provided claim values.")
        @ApiResponses({
                        @ApiResponse(responseCode = "200", description = "Account successfully identified", content = @Content(schema = @Schema(implementation = AccountResponse.class))),
                        @ApiResponse(responseCode = "400", description = "Invalid request data, unsupported account type, invalid claim definitions, or duplicate claims"),
                        @ApiResponse(responseCode = "404", description = "No identifier claim set found for business/account type, or no account matches the provided claims"),
                        @ApiResponse(responseCode = "409", description = "Multiple accounts found matching the provided claims - expected exactly one"),
                        @ApiResponse(responseCode = "401", description = "Authentication required"),
                        @ApiResponse(responseCode = "403", description = "Access denied")
        })
        @PostMapping("/businesses/{businessId}/accounts/identify")
        @Oauth2Authorize(permission = ACCOUNT_READ)
        public AccountResponse identifyAccount(
                        @PathVariable("businessId") UUID businessId,
                        @RequestParam("accountType") AccountType accountType,
                        @RequestBody @Valid @NotEmpty(message = "At least one claim identifier is required") List<ClaimIdentifierRequest> claimIdentifiers) {

                try {
                        IdentifyAccountByClaimsQuery query = IdentifyAccountByClaimsQuery.builder()
                                        .businessId(businessId)
                                        .accountType(accountType)
                                        .claimIdentifiers(claimIdentifiers)
                                        .build();

                        AccountJpaEntity account = identifyAccountQuery.identifyAccountByClaims(query);
                        return accountDtoMapper.toResponse(account);

                } catch (NoSuchElementException e) {
                        throw new ResponseStatusException(HttpStatus.NOT_FOUND, e.getMessage());
                } catch (IllegalStateException e) {
                        throw new ResponseStatusException(HttpStatus.CONFLICT, e.getMessage());
                } catch (IllegalArgumentException e) {
                        throw new ResponseStatusException(HttpStatus.BAD_REQUEST, e.getMessage());
                }
        }

        @GetMapping("/accounts/{accountId}/claims")
        @Oauth2Authorize(permission = ACCOUNT_READ)
        public AccountWithClaimsResponse getIdentityWithClaims(@PathVariable(value = "accountId") String accountId) {

                AccountJpaEntity account = getAccountQuery.getAccountById(accountId);
                List<ClaimValueJpaEntity> claimValuesList = getClaimValueQuery
                                .getClaimValuesByOwnerTypeAndOwnerId(OwnerType.ACCOUNT, UUID.fromString(accountId));

                return new AccountWithClaimsResponse(accountDtoMapper.toResponse(account),
                                claimValuesList.stream().map(claimValueDtoMapper::toResponse).toList());
        }
}
