package com.vusecurity.auth.scim.mapper;

import jakarta.xml.bind.annotation.*;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.BiConsumer;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Enhanced SCIM Filter Specification compliant mapper with setter support
 * 
 * Supports SCIM path expressions for both getting and setting values:
 * - emails[type eq "work"].value = "<EMAIL>"
 * - userName = "john.doe"
 * - emails[primary eq true and type eq "work"].value = "<EMAIL>"
 * - name.familyName = "NewLastName"
 */
public class FilterMapper {
    
    private static final Map<Class<?>, FilterMapper> CACHE = new ConcurrentHashMap<>();
    
    // SCIM Filter patterns (same as original)
    private static final Pattern FILTER_EXPRESSION = Pattern.compile(
        "([\\w.]+)\\s*(eq|ne|co|sw|ew|pr|gt|ge|lt|le)\\s*(.+)"
    );
    
    private static final Pattern COMPLEX_FILTER = Pattern.compile(
        "([\\w.]+)\\[(.+?)\\](?:\\.([\\w.]+))?"
    );
    
    private static final Pattern LOGICAL_OPERATOR = Pattern.compile(
        "\\s+(and|or)\\s+", Pattern.CASE_INSENSITIVE
    );
    
    private static final Pattern GROUPING = Pattern.compile(
        "\\(([^()]+)\\)"
    );
    
    private final Map<String, Function<Object, Object>> getters = new ConcurrentHashMap<>();
    private final Map<String, BiConsumer<Object, Object>> setters = new ConcurrentHashMap<>();
    private final Map<String, Class<?>> fieldTypes = new ConcurrentHashMap<>();
    
    private FilterMapper(Class<?> clazz) {
        buildAccessors(clazz);
    }
    
    public static FilterMapper forClass(Class<?> clazz) {
        return CACHE.computeIfAbsent(clazz, FilterMapper::new);
    }
    
    /**
     * Set value using SCIM path expression
     * 
     * Examples:
     * - setValue(user, "emails[type eq \"work\"].value", "<EMAIL>")
     * - setValue(user, "name.familyName", "NewLastName")
     * - setValue(user, "userName", "new.username")
     * - setValue(user, "emails[primary eq true].type", "business")
     */
    public boolean setValue(Object instance, String scimPath, Object value) {
        if (instance == null || scimPath == null || scimPath.trim().isEmpty()) {
            return false;
        }
        
        try {
            // Handle complex filter paths
            Matcher complexMatcher = COMPLEX_FILTER.matcher(scimPath);
            if (complexMatcher.matches()) {
                String collectionPath = complexMatcher.group(1);
                String filterExpression = complexMatcher.group(2);
                String targetPath = complexMatcher.group(3);
                
                return setComplexValue(instance, collectionPath, filterExpression, targetPath, value);
            } else {
                // Handle simple path navigation
                return setValueByPath(instance, scimPath, value);
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to set value at path: " + scimPath, e);
        }
    }
    
    /**
     * Set multiple values using SCIM path expression
     * This will set the value on ALL matching items in a collection
     */
    public int setValues(Object instance, String scimPath, Object value) {
        if (instance == null || scimPath == null || scimPath.trim().isEmpty()) {
            return 0;
        }
        
        int updateCount = 0;
        
        try {
            // Handle complex filter paths
            Matcher complexMatcher = COMPLEX_FILTER.matcher(scimPath);
            if (complexMatcher.matches()) {
                String collectionPath = complexMatcher.group(1);
                String filterExpression = complexMatcher.group(2);
                String targetPath = complexMatcher.group(3);
                
                // Get the collection
                Object collection = getFieldValue(instance, collectionPath, instance.getClass());
                if (collection == null) {
                    return 0;
                }
                
                // Convert to list
                List<Object> items = convertToList(collection);
                
                // Apply filter and set values on all matching items
                Predicate<Object> filterPredicate = parseFilter(filterExpression);
                for (Object item : items) {
                    if (item != null && filterPredicate.test(item)) {
                        if (targetPath != null) {
                            if (setValueByPath(item, targetPath, value)) {
                                updateCount++;
                            }
                        } else {
                            // Setting the entire item - this is complex and depends on use case
                            // For now, we'll skip this scenario
                        }
                    }
                }
            } else {
                // Simple path - single value
                if (setValueByPath(instance, scimPath, value)) {
                    updateCount = 1;
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to set values at path: " + scimPath, e);
        }
        
        return updateCount;
    }
    
    /**
     * Create new item in collection if filter doesn't match existing items
     * Returns true if new item was created, false if existing item was updated
     */
    public boolean setValueOrCreate(Object instance, String scimPath, Object value, 
                                   Function<Class<?>, Object> itemCreator) {
        if (instance == null || scimPath == null || scimPath.trim().isEmpty()) {
            return false;
        }
        
        try {
            Matcher complexMatcher = COMPLEX_FILTER.matcher(scimPath);
            if (complexMatcher.matches()) {
                String collectionPath = complexMatcher.group(1);
                String filterExpression = complexMatcher.group(2);
                String targetPath = complexMatcher.group(3);
                
                // Try to set existing value first
                if (setComplexValue(instance, collectionPath, filterExpression, targetPath, value)) {
                    return false; // Updated existing
                }
                
                // No existing item found, create new one
                Object collection = getFieldValue(instance, collectionPath, instance.getClass());
                if (collection instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<Object> list = (List<Object>) collection;
                    
                    // Create new item
                    Object newItem = itemCreator.apply(value.getClass());
                    if (newItem != null) {
                        // Set the target value if specified
                        if (targetPath != null) {
                            setValueByPath(newItem, targetPath, value);
                        }
                        
                        // Parse filter to set matching properties on new item
                        setFilterPropertiesOnNewItem(newItem, filterExpression);
                        
                        list.add(newItem);
                        return true; // Created new
                    }
                }
            } else {
                // Simple path
                return !setValue(instance, scimPath, value); // Return true if this creates new value
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to set or create value at path: " + scimPath, e);
        }
        
        return false;
    }
    
    private boolean setComplexValue(Object instance, String collectionPath, 
                                   String filterExpression, String targetPath, Object value) {
        // Get the collection
        Object collection = getFieldValue(instance, collectionPath, instance.getClass());
        if (collection == null) {
            return false;
        }
        
        // Convert to list
        List<Object> items = convertToList(collection);
        
        // Apply filter and set value on first matching item
        Predicate<Object> filterPredicate = parseFilter(filterExpression);
        for (Object item : items) {
            if (item != null && filterPredicate.test(item)) {
                if (targetPath != null) {
                    return setValueByPath(item, targetPath, value);
                } else {
                    // Setting properties directly on the collection item
                    // This would require parsing the filter and setting those properties
                    return setFilterPropertiesOnItem(item, filterExpression, value);
                }
            }
        }
        
        return false; // No matching item found
    }
    
    private boolean setFilterPropertiesOnItem(Object item, String filterExpression, Object value) {
        // This is complex - for now, we'll handle simple cases
        // In practice, you might want to set specific properties based on the filter
        return false;
    }
    
    /* old
    private void setFilterPropertiesOnNewItem(Object newItem, String filterExpression) {
        // Parse filter expression and set matching properties on new item
        try {
            // Handle simple equality filters to set properties on new items
            String[] conditions = filterExpression.split("\\s+and\\s+");
            
            for (String condition : conditions) {
                Matcher matcher = FILTER_EXPRESSION.matcher(condition.trim());
                if (matcher.matches()) {
                    String attribute = matcher.group(1);
                    String operator = matcher.group(2);
                    String filterValue = matcher.group(3).trim();
                    
                    // Remove quotes from string values
                    if (filterValue.startsWith("\"") && filterValue.endsWith("\"")) {
                        filterValue = filterValue.substring(1, filterValue.length() - 1);
                    }
                    
                    // Only set for equality conditions
                    if ("eq".equals(operator)) {
                        Object convertedValue = convertStringToType(filterValue, getFieldType(attribute));
                        setValueByPath(newItem, attribute, convertedValue);
                    }
                }
            }
        } catch (Exception e) {
            // Ignore errors in filter property setting
        }
    }
        */
    
    private boolean setValueByPath(Object instance, String path, Object value) {
        if (instance == null || path == null) {
            return false;
        }
        
        String[] segments = path.split("\\.");
        Object current = instance;
        Class<?> currentClass = instance.getClass();
        
        // Navigate to the parent of the final segment
        for (int i = 0; i < segments.length - 1; i++) {
            if (current == null) {
                return false;
            }
            
            current = getFieldValue(current, segments[i], currentClass);
            if (current != null) {
                currentClass = current.getClass();
            }
        }
        
        if (current == null) {
            return false;
        }
        
        // Set the final field
        String finalSegment = segments[segments.length - 1];
        return setFieldValue(current, finalSegment, value, currentClass);
    }
    
    private boolean setFieldValue(Object instance, String fieldName, Object value, Class<?> clazz) {
        FilterMapper mapper = forClass(clazz);
        BiConsumer<Object, Object> setter = mapper.setters.get(fieldName);
        
        if (setter == null) {
            throw new IllegalArgumentException("No setter found for field: " + fieldName + 
                " in class " + clazz.getSimpleName() + ". Available fields: " + mapper.setters.keySet());
        }
        
        try {
            // Convert value to appropriate type if necessary
            Class<?> fieldType = mapper.fieldTypes.get(fieldName);
            Object convertedValue = convertValueToType(value, fieldType);
            
            setter.accept(instance, convertedValue);
            return true;
        } catch (Exception e) {
            throw new RuntimeException("Failed to set field: " + fieldName, e);
        }
    }
    
    private Object convertValueToType(Object value, Class<?> targetType) {
        if (value == null || targetType == null) {
            return value;
        }
        
        if (targetType.isAssignableFrom(value.getClass())) {
            return value;
        }
        
        // Handle common conversions
        if (value instanceof String) {
            return convertStringToType((String) value, targetType);
        }
        
        return value;
    }
    
    private Object convertStringToType(String value, Class<?> targetType) {
        if (targetType == String.class) {
            return value;
        } else if (targetType == Boolean.class || targetType == boolean.class) {
            return Boolean.parseBoolean(value);
        } else if (targetType == Integer.class || targetType == int.class) {
            return Integer.parseInt(value);
        } else if (targetType == Long.class || targetType == long.class) {
            return Long.parseLong(value);
        } else if (targetType == Double.class || targetType == double.class) {
            return Double.parseDouble(value);
        } else if (targetType == LocalDateTime.class) {
            return LocalDateTime.parse(value, DateTimeFormatter.ISO_DATE_TIME);
        }
        
        return value;
    }

    /**
     * Filter collection using SCIM filter expression
     * 
     * Examples:
     * - filter(users, "userName eq \"john.doe\"")
     * - filter(users, "emails[type eq \"work\"].value")
     * - filter(users, "name.familyName co \"Smith\"")
     * - filter(users, "active eq true")
     * - filter(users, "emails[primary eq true and type eq \"work\"]")
     */
    public List<String> getStringValues(Object instance, String scimPath) {        
        List<String> results = this.getValues(instance, scimPath).stream().map(Object::toString).toList();
        
        return results;
    }

    /**
     * Get single value using SCIM path expression
     * 
     * Examples:
     * - getValue(user, "emails[type eq \"work\"].value")
     * - getValue(user, "name.familyName")
     */
    public String getStringValue(Object instance, String scimPath) {

        Object result = this.getValue(instance, scimPath);
        
        return Objects.isNull(result) ? null : result.toString();

    }
    
    // Original getter methods (unchanged)
    public List<Object> filter(List<Object> collection, String scimFilter) {
        if (collection == null || collection.isEmpty() || scimFilter == null || scimFilter.trim().isEmpty()) {
            return new ArrayList<>(collection != null ? collection : Collections.emptyList());
        }
        
        List<Object> results = new ArrayList<>();
        Predicate<Object> filterPredicate = parseFilter(scimFilter);
        
        for (Object item : collection) {
            if (item != null && filterPredicate.test(item)) {
                results.add(item);
            }
        }
        
        return results;
    }
    
    public Object getValue(Object instance, String scimPath) {
        if (instance == null || scimPath == null || scimPath.trim().isEmpty()) {
            return null;
        }
        
        // Handle complex filter paths
        Matcher complexMatcher = COMPLEX_FILTER.matcher(scimPath);
        if (complexMatcher.matches()) {
            String collectionPath = complexMatcher.group(1);
            String filterExpression = complexMatcher.group(2);
            String targetPath = complexMatcher.group(3);
            
            // Get the collection
            Object collection = getFieldValue(instance, collectionPath, instance.getClass());
            if (collection == null) {
                return null;
            }
            
            // Convert to list
            List<Object> items = convertToList(collection);
            
            // Apply filter
            Predicate<Object> filterPredicate = parseFilter(filterExpression);
            for (Object item : items) {
                if (item != null && filterPredicate.test(item)) {
                    if (targetPath != null) {
                        return getValue(item, targetPath);
                    } else {
                        return item;
                    }
                }
            }
            
            return null;
        }
        
        // Handle simple path navigation
        return getValueByPath(instance, scimPath);
    }

    /**
     * Get all values using SCIM path expression
     */
    public List<String> getValuesToString(Object instance, String scimPath) {        
        List<String> results = new ArrayList<>();

        results = this.getValues(instance, scimPath).stream().map(entry -> entry.toString()).toList();
        
        return results;
    }
    
    // ... (include all other original getter methods: getValues, matches, etc.)
    // [Previous getter implementation methods remain the same]

    /**
     * Get all values using SCIM path expression
     */
    public List<Object> getValues(Object instance, String scimPath) {
        if (instance == null || scimPath == null || scimPath.trim().isEmpty()) {
            return Collections.emptyList();
        }
        
        List<Object> results = new ArrayList<>();
        
        // Handle complex filter paths
        Matcher complexMatcher = COMPLEX_FILTER.matcher(scimPath);
        if (complexMatcher.matches()) {
            String collectionPath = complexMatcher.group(1);
            String filterExpression = complexMatcher.group(2);
            String targetPath = complexMatcher.group(3);
            
            // Get the collection
            Object collection = getFieldValue(instance, collectionPath, instance.getClass());
            if (collection == null) {
                return results;
            }
            
            // Convert to list
            List<Object> items = convertToList(collection);
            
            // Apply filter
            Predicate<Object> filterPredicate = parseFilter(filterExpression);
            for (Object item : items) {
                if (item != null && filterPredicate.test(item)) {
                    if (targetPath != null) {
                        Object value = getValue(item, targetPath);
                        if (value != null) {
                            results.add(value);
                        }
                    } else {
                        results.add(item);
                    }
                }
            }
        } else {
            // Simple path - single value
            Object value = getValueByPath(instance, scimPath);
            if (value != null) {
                results.add(value);
            }
        }
        
        return results;
    }
    
    /**
     * Check if instance matches SCIM filter
     */
    public boolean matches(Object instance, String scimFilter) {
        if (instance == null || scimFilter == null || scimFilter.trim().isEmpty()) {
            return false;
        }
        
        Predicate<Object> filterPredicate = parseFilter(scimFilter);
        return filterPredicate.test(instance);
    }

    public String extractSimpleFilteredAttribute(String scimPath){
        if ( scimPath == null || scimPath.trim().isEmpty()) {
            return null;
        }
        
        List<Object> results = new ArrayList<>();
        
        // Handle complex filter paths
        Matcher complexMatcher = COMPLEX_FILTER.matcher(scimPath);
        if (complexMatcher.matches()) {
            return complexMatcher.group(1);
        } else {
            return scimPath;
        }
    }
    
    private Predicate<Object> parseFilter(String filter) {
        if (filter == null || filter.trim().isEmpty()) {
            return obj -> true;
        }
        
        filter = filter.trim();
        return parseLogicalExpression(filter);
    }
    
    private Predicate<Object> parseLogicalExpression(String expression) {
        // Handle parentheses first
        if (expression.contains("(")) {
            Matcher groupMatcher = GROUPING.matcher(expression);
            if (groupMatcher.find()) {
                String group = groupMatcher.group(1);
                Predicate<Object> groupPredicate = parseLogicalExpression(group);
                return groupPredicate;
            }
        }
        
        // Split by OR first (lower precedence)
        String[] orParts = expression.split("\\s+or\\s+", 2);
        if (orParts.length > 1) {
            Predicate<Object> left = parseLogicalExpression(orParts[0].trim());
            Predicate<Object> right = parseLogicalExpression(orParts[1].trim());
            return left.or(right);
        }
        
        // Split by AND (higher precedence)
        String[] andParts = expression.split("\\s+and\\s+", 2);
        if (andParts.length > 1) {
            Predicate<Object> left = parseLogicalExpression(andParts[0].trim());
            Predicate<Object> right = parseLogicalExpression(andParts[1].trim());
            return left.and(right);
        }
        
        // Parse single filter expression
        return parseSingleFilter(expression);
    }
    
    private Predicate<Object> parseSingleFilter(String filter) {
        Matcher matcher = FILTER_EXPRESSION.matcher(filter);
        if (!matcher.matches()) {
            throw new IllegalArgumentException("Invalid SCIM filter expression: " + filter);
        }
        
        String attribute = matcher.group(1);
        String operator = matcher.group(2);
        String value = matcher.group(3).trim();
        
        // Remove quotes from string values
        if (value.startsWith("\"") && value.endsWith("\"")) {
            value = value.substring(1, value.length() - 1);
        }
        
        return createFilterPredicate(attribute, operator, value);
    }
    
    private Predicate<Object> createFilterPredicate(String attribute, String operator, String expectedValue) {
        return instance -> {
            try {
                Object actualValue = getValueByPath(instance, attribute);
                return evaluateCondition(actualValue, operator, expectedValue);
            } catch (Exception e) {
                return false;
            }
        };
    }
    
    private boolean evaluateCondition(Object actualValue, String operator, String expectedValue) {
        if (actualValue == null) {
            return "pr".equals(operator) ? false : "ne".equals(operator);
        }
        
        String actualStr = actualValue.toString();
        
        switch (operator.toLowerCase()) {
            case "eq": // equal
                return compareValues(actualValue, expectedValue) == 0;
            case "ne": // not equal
                return compareValues(actualValue, expectedValue) != 0;
            case "co": // contains
                return actualStr.toLowerCase().contains(expectedValue.toLowerCase());
            case "sw": // starts with
                return actualStr.toLowerCase().startsWith(expectedValue.toLowerCase());
            case "ew": // ends with
                return actualStr.toLowerCase().endsWith(expectedValue.toLowerCase());
            case "pr": // present (not null)
                return actualValue != null;
            case "gt": // greater than
                return compareValues(actualValue, expectedValue) > 0;
            case "ge": // greater than or equal
                return compareValues(actualValue, expectedValue) >= 0;
            case "lt": // less than
                return compareValues(actualValue, expectedValue) < 0;
            case "le": // less than or equal
                return compareValues(actualValue, expectedValue) <= 0;
            default:
                throw new IllegalArgumentException("Unsupported SCIM operator: " + operator);
        }
    }
    
    private int compareValues(Object actualValue, String expectedValue) {
        // Handle boolean values
        if (actualValue instanceof Boolean) {
            Boolean actual = (Boolean) actualValue;
            Boolean expected = Boolean.parseBoolean(expectedValue);
            return actual.compareTo(expected);
        }
        
        // Handle numeric values
        if (actualValue instanceof Number) {
            Double actual = ((Number) actualValue).doubleValue();
            try {
                Double expected = Double.parseDouble(expectedValue);
                return actual.compareTo(expected);
            } catch (NumberFormatException e) {
                return actualValue.toString().compareTo(expectedValue);
            }
        }
        
        // Handle dates (ISO 8601 format)
        if (actualValue instanceof LocalDateTime || expectedValue.matches("\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}.*")) {
            try {
                LocalDateTime actual = actualValue instanceof LocalDateTime ? 
                    (LocalDateTime) actualValue : 
                    LocalDateTime.parse(actualValue.toString(), DateTimeFormatter.ISO_DATE_TIME);
                LocalDateTime expected = LocalDateTime.parse(expectedValue, DateTimeFormatter.ISO_DATE_TIME);
                return actual.compareTo(expected);
            } catch (Exception e) {
                return actualValue.toString().compareTo(expectedValue);
            }
        }
        
        // Default string comparison
        return actualValue.toString().compareTo(expectedValue);
    }
    
    private Object getValueByPath(Object instance, String path) {
        if (instance == null || path == null) {
            return null;
        }
        
        String[] segments = path.split("\\.");
        Object current = instance;
        Class<?> currentClass = instance.getClass();
        
        for (String segment : segments) {
            if (current == null) {
                return null;
            }
            
            current = getFieldValue(current, segment, currentClass);
            if (current != null) {
                currentClass = current.getClass();
            }
        }
        
        return current;
    }
    
    private Object getFieldValue(Object instance, String fieldName, Class<?> clazz) {
        FilterMapper mapper = forClass(clazz);
        Function<Object, Object> accessor = mapper.getters.get(fieldName);
        
        if (accessor == null) {
            throw new IllegalArgumentException("No JAXB field found with name: " + fieldName + 
                " in class " + clazz.getSimpleName() + ". Available fields: " + mapper.getters.keySet());
        }
        
        return accessor.apply(instance);
    }
    
    private List<Object> convertToList(Object collection) {
        if (collection == null) {
            return Collections.emptyList();
        }
        
        List<Object> items = new ArrayList<>();
        if (collection.getClass().isArray()) {
            items.addAll(Arrays.asList((Object[]) collection));
        } else if (collection instanceof List) {
            items.addAll((List<?>) collection);
        } else {
            items.add(collection);
        }
        
        return items;
    }
    
    // JAXB accessor building (enhanced with setters)
    private void buildAccessors(Class<?> clazz) {
        for (Field field : getAllFields(clazz)) {
            String xmlName = getXmlFieldName(field);
            if (xmlName != null) {
                field.setAccessible(true);
                
                // Getter
                Function<Object, Object> getter = instance -> {
                    try {
                        return field.get(instance);
                    } catch (IllegalAccessException e) {
                        throw new RuntimeException("Cannot access field: " + field.getName(), e);
                    }
                };
                
                // Setter
                BiConsumer<Object, Object> setter = (instance, value) -> {
                    try {
                        field.set(instance, value);
                    } catch (IllegalAccessException e) {
                        throw new RuntimeException("Cannot set field: " + field.getName(), e);
                    }
                };
                
                getters.put(xmlName, getter);
                setters.put(xmlName, setter);
                fieldTypes.put(xmlName, field.getType());
                
                if (!xmlName.equals(field.getName())) {
                    getters.put(field.getName(), getter);
                    setters.put(field.getName(), setter);
                    fieldTypes.put(field.getName(), field.getType());
                }
            }
        }
        
        for (Method method : clazz.getDeclaredMethods()) {
            if (isGetter(method)) {
                String xmlName = getXmlMethodName(method);
                if (xmlName != null) {
                    method.setAccessible(true);
                    
                    // Getter
                    Function<Object, Object> getter = instance -> {
                        try {
                            return method.invoke(instance);
                        } catch (Exception e) {
                            throw new RuntimeException("Cannot invoke method: " + method.getName(), e);
                        }
                    };
                    
                    // Find corresponding setter method
                    String setterName = "set" + method.getName().substring(3);
                    Method setterMethod = findSetterMethod(clazz, setterName, method.getReturnType());
                    
                    if (setterMethod != null) {
                        setterMethod.setAccessible(true);
                        BiConsumer<Object, Object> setter = (instance, value) -> {
                            try {
                                setterMethod.invoke(instance, value);
                            } catch (Exception e) {
                                throw new RuntimeException("Cannot invoke setter: " + setterMethod.getName(), e);
                            }
                        };
                        
                        setters.put(xmlName, setter);
                        String fieldName = getFieldNameFromMethod(method.getName());
                        if (!xmlName.equals(fieldName)) {
                            setters.put(fieldName, setter);
                        }
                    }
                    
                    getters.put(xmlName, getter);
                    fieldTypes.put(xmlName, method.getReturnType());
                    String fieldName = getFieldNameFromMethod(method.getName());
                    if (!xmlName.equals(fieldName)) {
                        getters.put(fieldName, getter);
                        fieldTypes.put(fieldName, method.getReturnType());
                    }
                }
            }
        }
    }
    
    private Method findSetterMethod(Class<?> clazz, String setterName, Class<?> paramType) {
        try {
            return clazz.getDeclaredMethod(setterName, paramType);
        } catch (NoSuchMethodException e) {
            // Try with parent classes
            Class<?> parent = clazz.getSuperclass();
            if (parent != null && parent != Object.class) {
                return findSetterMethod(parent, setterName, paramType);
            }
            return null;
        }
    }
    
    // ... (include all other helper methods from original implementation)
    
    private Field[] getAllFields(Class<?> clazz) {
        if (clazz == null || clazz == Object.class) {
            return new Field[0];
        }
        
        Field[] currentFields = clazz.getDeclaredFields();
        Field[] parentFields = getAllFields(clazz.getSuperclass());
        
        Field[] allFields = new Field[currentFields.length + parentFields.length];
        System.arraycopy(currentFields, 0, allFields, 0, currentFields.length);
        System.arraycopy(parentFields, 0, allFields, currentFields.length, parentFields.length);
        
        return allFields;
    }
    
    private String getXmlFieldName(Field field) {
        XmlElement xmlElement = field.getAnnotation(XmlElement.class);
        if (xmlElement != null) {
            return getEffectiveName(xmlElement.name(), field.getName());
        }
        
        XmlAttribute xmlAttribute = field.getAnnotation(XmlAttribute.class);
        if (xmlAttribute != null) {
            return getEffectiveName(xmlAttribute.name(), field.getName());
        }
        
        XmlValue xmlValue = field.getAnnotation(XmlValue.class);
        if (xmlValue != null) {
            return field.getName();
        }
        
        XmlElementWrapper xmlWrapper = field.getAnnotation(XmlElementWrapper.class);
        if (xmlWrapper != null) {
            return getEffectiveName(xmlWrapper.name(), field.getName());
        }
        
        return null;
    }
    
    private String getXmlMethodName(Method method) {
        XmlElement xmlElement = method.getAnnotation(XmlElement.class);
        if (xmlElement != null) {
            String fieldName = getFieldNameFromMethod(method.getName());
            return getEffectiveName(xmlElement.name(), fieldName);
        }
        
        XmlAttribute xmlAttribute = method.getAnnotation(XmlAttribute.class);
        if (xmlAttribute != null) {
            String fieldName = getFieldNameFromMethod(method.getName());
            return getEffectiveName(xmlAttribute.name(), fieldName);
        }
        
        return null;
    }
    
    private String getEffectiveName(String annotationName, String defaultName) {
        return "##default".equals(annotationName) ? defaultName : annotationName;
    }
    
    private boolean isGetter(Method method) {
        return method.getName().startsWith("get") 
               && method.getParameterCount() == 0 
               && !void.class.equals(method.getReturnType());
    }
    
    private String getFieldNameFromMethod(String methodName) {
        if (methodName.startsWith("get") && methodName.length() > 3) {
            return Character.toLowerCase(methodName.charAt(3)) + methodName.substring(4);
        }
        return methodName;
    }
    
    // Public API methods
    public boolean hasField(String fieldName) {
        return getters.containsKey(fieldName);
    }
    
    public boolean hasSetter(String fieldName) {
        return setters.containsKey(fieldName);
    }
    
    public Set<String> getAvailableFields() {
        return getters.keySet();
    }
    
    public Set<String> getSettableFields() {
        return setters.keySet();
    }
    
    public Class<?> getFieldType(String fieldName) {
        return fieldTypes.get(fieldName);
    }

    // Enhanced setFilterPropertiesOnNewItem method
    private void setFilterPropertiesOnNewItem(Object newItem, String filterExpression) {
        try {
            // Handle logical operators by parsing each condition
            List<String> conditions = parseFilterConditions(filterExpression);
            
            for (String condition : conditions) {
                Matcher matcher = FILTER_EXPRESSION.matcher(condition.trim());
                if (matcher.matches()) {
                    String attribute = matcher.group(1);
                    String operator = matcher.group(2);
                    String filterValue = matcher.group(3).trim();
                    
                    // Remove quotes from string values
                    if (filterValue.startsWith("\"") && filterValue.endsWith("\"")) {
                        filterValue = filterValue.substring(1, filterValue.length() - 1);
                    }
                    
                    // Only set for equality conditions
                    if ("eq".equals(operator)) {
                        try {
                            Class<?> fieldType = getFieldType(attribute);
                            if (fieldType == null) {
                                // Try to get field type from the new item's class
                                FilterMapper itemMapper = forClass(newItem.getClass());
                                fieldType = itemMapper.getFieldType(attribute);
                            }
                            
                            Object convertedValue = convertStringToType(filterValue, fieldType);
                            setValueByPath(newItem, attribute, convertedValue);
                        } catch (Exception e) {
                            // Ignore individual field setting errors
                        }
                    }
                }
            }
        } catch (Exception e) {
            // Ignore errors in filter property setting
        }
    }

    private List<String> parseFilterConditions(String filterExpression) {
        List<String> conditions = new ArrayList<>();
        
        // Simple split by 'and' - this could be enhanced for more complex parsing
        if (filterExpression.toLowerCase().contains(" and ")) {
            String[] parts = filterExpression.split("\\s+and\\s+", -1);
            for (String part : parts) {
                conditions.add(part.trim());
            }
        } else {
            conditions.add(filterExpression.trim());
        }
        
        return conditions;
    }


    /**
     * Enhanced setValue that can auto-create intermediate objects for simple paths
     * 
     * Examples:
     * - setValue(user, "name.givenName", "John") -> Creates Name object if null
     * - setValue(user, "address.streetAddress", "123 Main St") -> Creates Address object if null
     * - setValue(user, "manager.userName", "boss") -> Creates Manager object if null
     */
    public boolean setValueWithAutoPath(Object instance, String scimPath, Object value) {
        return setValueWithAutoPath(instance, scimPath, value, null);
    }

    /**
     * Enhanced setValue with custom object creators for intermediate objects
     */
    public boolean setValueWithAutoPath(Object instance, String scimPath, Object value, 
                                    Map<String, Function<Class<?>, Object>> pathCreators) {
        if (instance == null || scimPath == null || scimPath.trim().isEmpty()) {
            return false;
        }
        
        try {
            // Handle complex filter paths (existing logic)
            Matcher complexMatcher = COMPLEX_FILTER.matcher(scimPath);
            if (complexMatcher.matches()) {
                // Use existing complex path logic
                return setValueOrCreate(instance, scimPath, value, 
                    pathCreators != null && !pathCreators.isEmpty() ? 
                    pathCreators.values().iterator().next() : null);
            } else {
                // Handle simple path navigation with auto-creation
                return setValueByPathWithCreation(instance, scimPath, value, pathCreators);
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to set value at path: " + scimPath, e);
        }
    }

    /**
     * Enhanced path navigation that creates intermediate objects when needed
     */
    private boolean setValueByPathWithCreation(Object instance, String path, Object value, 
                                            Map<String, Function<Class<?>, Object>> pathCreators) {
        if (instance == null || path == null) {
            return false;
        }
        
        String[] segments = path.split("\\.");
        Object current = instance;
        Class<?> currentClass = instance.getClass();
        
        // Navigate to the parent of the final segment, creating objects as needed
        for (int i = 0; i < segments.length - 1; i++) {
            if (current == null) {
                return false;
            }
            
            String segment = segments[i];
            Object nextObject = null;
            
            try {
                // Try to get existing object
                nextObject = getFieldValue(current, segment, currentClass);
            } catch (Exception e) {
                // Field might not exist, handle later
            }
            
            // If object is null, try to create it
            if (nextObject == null) {
                Class<?> fieldType = getFieldTypeForPath(currentClass, segment);
                if (fieldType == null) {
                    throw new IllegalArgumentException(
                        "Cannot determine field type for: " + segment + " in class " + currentClass.getSimpleName());
                }
                
                // Create new object
                Object newObject = createObjectForPath(segment, fieldType, pathCreators);
                if (newObject == null) {
                    throw new RuntimeException(
                        "Cannot create object of type " + fieldType.getSimpleName() + " for path segment: " + segment);
                }
                
                // Set the new object on the current object
                if (!setFieldValue(current, segment, newObject, currentClass)) {
                    return false;
                }
                
                nextObject = newObject;
            }
            
            current = nextObject;
            currentClass = nextObject.getClass();
        }
        
        if (current == null) {
            return false;
        }
        
        // Set the final field
        String finalSegment = segments[segments.length - 1];
        return setFieldValue(current, finalSegment, value, currentClass);
    }

    /**
     * Get field type for a given path segment
     */
    private Class<?> getFieldTypeForPath(Class<?> clazz, String fieldName) {
        // Try to get from cached field types first
        FilterMapper mapper = forClass(clazz);
        Class<?> fieldType = mapper.fieldTypes.get(fieldName);
        if (fieldType != null) {
            return fieldType;
        }
        
        // Try to find field directly
        Field field = findFieldByName(clazz, fieldName);
        if (field != null) {
            return field.getType();
        }
        
        // Try to find getter method
        Method getter = findGetterMethodByFieldName(clazz, fieldName);
        if (getter != null) {
            return getter.getReturnType();
        }
        
        return null;
    }

    /**
     * Find field by name, including XML-mapped names
     */
    private Field findFieldByName(Class<?> clazz, String fieldName) {
        for (Field field : getAllFields(clazz)) {
            String xmlName = getXmlFieldName(field);
            if (fieldName.equals(xmlName) || fieldName.equals(field.getName())) {
                return field;
            }
        }
        return null;
    }

    /**
     * Find getter method by field name
     */
    private Method findGetterMethodByFieldName(Class<?> clazz, String fieldName) {
        for (Method method : clazz.getDeclaredMethods()) {
            if (isGetter(method)) {
                String xmlName = getXmlMethodName(method);
                String methodFieldName = getFieldNameFromMethod(method.getName());
                if (fieldName.equals(xmlName) || fieldName.equals(methodFieldName)) {
                    return method;
                }
            }
        }
        return null;
    }

    /**
     * Create object for a path segment
     */
    private Object createObjectForPath(String pathSegment, Class<?> fieldType, 
                                    Map<String, Function<Class<?>, Object>> pathCreators) {
        // Try custom creator first
        if (pathCreators != null && pathCreators.containsKey(pathSegment)) {
            Object created = pathCreators.get(pathSegment).apply(fieldType);
            if (created != null) {
                return created;
            }
        }
        
        // Try default creation
        return createDefaultInstance(fieldType);
    }

    /**
     * Enhanced createDefaultInstance with better constructor handling
     */
    private Object createDefaultInstance(Class<?> clazz) {
        if (clazz == null) {
            return null;
        }
        
        try {
            // Try no-arg constructor first
            java.lang.reflect.Constructor<?> defaultConstructor = clazz.getDeclaredConstructor();
            defaultConstructor.setAccessible(true);
            return defaultConstructor.newInstance();
        } catch (NoSuchMethodException e) {
            // Try to find any constructor and use default values
            try {
                java.lang.reflect.Constructor<?>[] constructors = clazz.getDeclaredConstructors();
                if (constructors.length > 0) {
                    // Sort by parameter count (prefer fewer parameters)
                    java.util.Arrays.sort(constructors, 
                        java.util.Comparator.comparing(c -> c.getParameterCount()));
                    
                    java.lang.reflect.Constructor<?> constructor = constructors[0];
                    constructor.setAccessible(true);
                    Class<?>[] paramTypes = constructor.getParameterTypes();
                    Object[] params = new Object[paramTypes.length];
                    
                    // Fill with default values
                    for (int i = 0; i < paramTypes.length; i++) {
                        params[i] = getDefaultValueForType(paramTypes[i]);
                    }
                    
                    return constructor.newInstance(params);
                }
            } catch (Exception e2) {
                // Could not create instance
            }
        } catch (Exception e) {
            // Could not create instance
        }
        
        return null;
    }

    /**
     * Get default value for a type
     */
    private Object getDefaultValueForType(Class<?> type) {
        if (type.isPrimitive()) {
            if (type == boolean.class) return false;
            if (type == byte.class) return (byte) 0;
            if (type == short.class) return (short) 0;
            if (type == int.class) return 0;
            if (type == long.class) return 0L;
            if (type == float.class) return 0.0f;
            if (type == double.class) return 0.0d;
            if (type == char.class) return '\0';
        } else if (type == String.class) {
            return ""; // Empty string instead of null
        }
        return null;
    }

    // Convenience methods for common use cases

    /**
     * Set value with auto-creation using default constructors
     */
    public boolean setValueAutoCreate(Object instance, String scimPath, Object value) {
        return setValueWithAutoPath(instance, scimPath, value, null);
    }

    /**
     * Set value with specific creator for a path segment
     */
    public boolean setValueWithCreator(Object instance, String scimPath, Object value, 
                                    String pathSegment, Function<Class<?>, Object> creator) {
        Map<String, Function<Class<?>, Object>> creators = new HashMap<>();
        creators.put(pathSegment, creator);
        return setValueWithAutoPath(instance, scimPath, value, creators);
    }
}