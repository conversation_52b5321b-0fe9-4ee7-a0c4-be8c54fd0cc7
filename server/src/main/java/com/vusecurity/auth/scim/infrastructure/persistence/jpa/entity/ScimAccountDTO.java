package com.vusecurity.auth.scim.infrastructure.persistence.jpa.entity;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.RoleJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.contracts.enums.AccountLifecycleState;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityProviderJpaEntity;
import com.vusecurity.business.domain.Business;

import lombok.AllArgsConstructor;

@AllArgsConstructor
public class ScimAccountDTO {

    private AccountJpaEntity account;
    private List<ClaimValueJpaEntity> claims;

    public ScimAccountDTO(AccountJpaEntity account){
        this.account = account;
    }

    public AccountJpaEntity getAccount(){
        return this.account;
    }

    public UUID getIdentityId() {
        return this.account.getIdentityId();
    }

    public UUID getBusinessId() {
        return this.account.getBusinessId();
    }

    public UUID getIdentityProviderId() {
        return this.account.getIdentityProviderId();
    }

    // Getters
    public Business getBusiness() {
        return this.account.getBusiness();
    }

    public IdentityJpaEntity getIdentity() {
        return this.account.getIdentity();
    }

    public AccountType getAccountType() {
        return this.account.getAccountType();
    }

    public AccountLifecycleState getLifecycleState() {
        return this.account.getLifecycleState();
    }

    public UUID getMergePrimary() {
        return this.account.getMergePrimary();
    }

    public UUID getMergeSecondary() {
        return this.account.getMergeSecondary();
    }

    public IdentityProviderJpaEntity getIdentityProvider() {
        return this.account.getIdentityProvider();
    }

    public Set<RoleJpaEntity> getRoles() {
        return this.account.getRoles();
    }

    // Setters
    public void setBusiness(Business business) {
        this.account.setBusiness(business);
    }

    public void setIdentity(IdentityJpaEntity identity) {
        this.account.setIdentity(identity);
    }

    public void setAccountType(AccountType accountType) {
        this.account.setAccountType(accountType);
    }

    public void setLifecycleState(AccountLifecycleState lifecycleState) {
        this.account.setLifecycleState(lifecycleState);
    }

    public void setMergePrimary(UUID mergePrimary) {
        this.account.setMergePrimary(mergePrimary);
    }

    public void setMergeSecondary(UUID mergeSecondary) {
        this.account.setMergeSecondary(mergeSecondary);
    }

    public void setIdentityProvider(IdentityProviderJpaEntity identityProvider) {
        this.account.setIdentityProvider(identityProvider);
    }

    // Business behavior methods
    public void activate() {
        this.account.activate();
    }

    public void suspend() {
        this.account.suspend();
    }

    public void deactivate() {
        this.account.deactivate();
    }

    public boolean isActive() {
        return this.account.isActive();
    }

    public boolean isSuspended() {
        return this.account.isSuspended();
    }

    public boolean isInactive() {
        return this.account.isInactive();
    }

    public void addMetadata(String key, Object value) {
        this.account.addMetadata(key, value);
    }

    public void removeMetadata(String key) {
        this.account.removeMetadata(key);
    }

    public void assignRole(String roleName) {
        this.account.assignRole(roleName);
    }

    public void removeRole(String roleName) {
        this.account.removeRole(roleName);
    }

    public boolean hasRole(String roleName) {
        return this.account.hasRole(roleName);
    }

    public Set<String> getRoleNames() {
        return this.account.getRoleNames();
    }

    // Custom getters for defensive copies
    public Map<String, Object> getMetadata() {
        return this.account.getMetadata();
    }

    // Custom setters for defensive copies
    public void setMetadata(Map<String, Object> metadata) {
        this.account.setMetadata(metadata);
    }

    public void setRoles(Set<RoleJpaEntity> roles) {
        this.setRoles(roles);
    }


    public List<ClaimValueJpaEntity> getClaims() {
        return claims;
    }

    public void setClaims(List<ClaimValueJpaEntity> claims) {
        this.claims = claims;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((account == null) ? 0 : account.hashCode());
        result = prime * result + ((claims == null) ? 0 : claims.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        ScimAccountDTO other = (ScimAccountDTO) obj;
        if (account == null) {
            if (other.account != null)
                return false;
        } else if (!account.equals(other.account))
            return false;
        if (claims == null) {
            if (other.claims != null)
                return false;
        } else if (!claims.equals(other.claims))
            return false;
        return true;
    }

    
    
}
