package com.vusecurity.auth.shared.infrastructure.security.mfa;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.vusecurity.auth.AppConfigProperties;
import com.vusecurity.multitenant.TenantContext;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.HttpClientErrorException;

import java.util.UUID;

/**
 * Client service for communicating with the Factor Server API.
 * Handles password and TOTP validation requests.
 */
@Service
public class FactorServerClient {

    private static final Logger logger = LoggerFactory.getLogger(FactorServerClient.class);

    private final RestTemplate restTemplate;
    private final AppConfigProperties appConfig;

    public FactorServerClient(RestTemplate restTemplate, AppConfigProperties appConfig) {
        this.restTemplate = restTemplate;
        this.appConfig = appConfig;
    }

    /**
     * Validates password against the factor server
     */
    public boolean validatePassword(UUID accountId, String password, String businessId, String channelId) {
        try {
            String url = appConfig.getFactorsServer().getUrl() + "/api/v1/passwords/login";
            
            HttpHeaders headers = createHeaders(businessId, channelId);
            
            ValidatePasswordRequest request = new ValidatePasswordRequest(accountId, password);
            HttpEntity<ValidatePasswordRequest> entity = new HttpEntity<>(request, headers);
            
            logger.debug("Validating password for account: {}", accountId);
            
            ResponseEntity<Void> response = restTemplate.exchange(
                url, 
                HttpMethod.POST, 
                entity, 
                Void.class
            );
            
            boolean isValid = response.getStatusCode() == HttpStatus.OK;
            logger.debug("Password validation result for account {}: {}", accountId, isValid);
            
            return isValid;
            
        } catch (HttpClientErrorException e) {
            logger.debug("Password validation failed for account {}: {} - {}", 
                        accountId, e.getStatusCode(), e.getMessage());
            return false;
        } catch (Exception e) {
            logger.error("Error validating password for account {}: {}", accountId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Validates TOTP against the factor server
     */
    public boolean validateTotp(UUID accountId, String totp, String businessId, String channelId) {
        try {
            String url = appConfig.getFactorsServer().getUrl() + "/api/v1/otp/time/login";
            
            HttpHeaders headers = createHeaders(businessId, channelId);
            
            ValidateTotpRequest request = new ValidateTotpRequest(accountId, totp);
            HttpEntity<ValidateTotpRequest> entity = new HttpEntity<>(request, headers);
            
            logger.debug("Validating TOTP for account: {}", accountId);
            
            ResponseEntity<Void> response = restTemplate.exchange(
                url, 
                HttpMethod.POST, 
                entity, 
                Void.class
            );
            
            boolean isValid = response.getStatusCode() == HttpStatus.OK;
            logger.debug("TOTP validation result for account {}: {}", accountId, isValid);
            
            return isValid;
            
        } catch (HttpClientErrorException e) {
            logger.debug("TOTP validation failed for account {}: {} - {}", 
                        accountId, e.getStatusCode(), e.getMessage());
            return false;
        } catch (Exception e) {
            logger.error("Error validating TOTP for account {}: {}", accountId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Creates HTTP headers required by the factor server API
     */
    private HttpHeaders createHeaders(String businessId, String channelId) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set(appConfig.getMultitenant().getIdHeader(), TenantContext.getCurrentTenant());
        headers.set("x-apikey", appConfig.getFactorsServer().getApikey());
        headers.set("X-Business-Id", businessId);
        headers.set("X-Channel-Id", channelId);
        
        // Add multitenant header if available
        if (appConfig.getMultitenant() != null && appConfig.getMultitenant().getIdHeader() != null) {
            // This would typically be set from the current request context
            // For now, we'll leave it as optional
        }
        
        return headers;
    }

    /**
     * Request DTO for password validation
     */
    public static class ValidatePasswordRequest {
        @JsonProperty("accountId")
        private UUID accountId;
        
        @JsonProperty("password")
        private String password;

        public ValidatePasswordRequest(UUID accountId, String password) {
            this.accountId = accountId;
            this.password = password;
        }

        // Getters and setters
        public UUID getAccountId() { return accountId; }
        public void setAccountId(UUID accountId) { this.accountId = accountId; }
        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }
    }

    /**
     * Request DTO for TOTP validation
     */
    public static class ValidateTotpRequest {
        @JsonProperty("accountId")
        private UUID accountId;
        
        @JsonProperty("otp")
        private String otp;

        public ValidateTotpRequest(UUID accountId, String otp) {
            this.accountId = accountId;
            this.otp = otp;
        }

        // Getters and setters
        public UUID getAccountId() { return accountId; }
        public void setAccountId(UUID accountId) { this.accountId = accountId; }
        public String getOtp() { return otp; }
        public void setOtp(String otp) { this.otp = otp; }
    }

    /**
     * Created password into the factor server
     */
    public boolean createPassword(UUID accountId, String password, String businessId) {
        try {
            String url = appConfig.getFactorsServer().getUrl() + "/api/v1/passwords";

            HttpHeaders headers = createHeaders(businessId, businessId);

            ValidatePasswordRequest request = new ValidatePasswordRequest(accountId, password);
            HttpEntity<ValidatePasswordRequest> entity = new HttpEntity<>(request, headers);

            logger.debug("Creating password for account: {}", accountId);

            ResponseEntity<Void> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    entity,
                    Void.class
            );

            boolean isCreated = response.getStatusCode() == HttpStatus.CREATED;
            logger.debug("Password creation result for account {}: {}", accountId, isCreated);

            return isCreated;

        } catch (HttpClientErrorException e) {
            logger.debug("Password creation failed for account {}: {} - {}",
                    accountId, e.getStatusCode(), e.getMessage());
            return false;
        } catch (Exception e) {
            logger.error("Error creating password for account {}: {}", accountId, e.getMessage(), e);
            return false;
        }
    }
}
