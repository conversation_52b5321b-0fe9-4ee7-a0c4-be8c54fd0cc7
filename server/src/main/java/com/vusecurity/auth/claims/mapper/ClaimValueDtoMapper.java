package com.vusecurity.auth.claims.mapper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimvalue.ClaimValueResponse;
import com.vusecurity.auth.contracts.api.v1.dto.shared.BaseResponse;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Mapper for converting between ClaimValueJpaEntity and DTOs.
 * Manual mapping implementation following the project's preference over MapStruct.
 */
@Component
@RequiredArgsConstructor
public class ClaimValueDtoMapper {

    private static final Logger log = LoggerFactory.getLogger(ClaimValueDtoMapper.class);

    private final ObjectMapper objectMapper;

    /**
     * Convert ClaimValueJpaEntity to ClaimValueResponse.
     * @param entity the JPA entity
     * @return the response DTO
     */
    public ClaimValueResponse toResponse(ClaimValueJpaEntity entity) {
        if (entity == null) {
            return null;
        }

        ClaimValueResponse response = new ClaimValueResponse();
        response.setId(entity.getId());
        response.setClaimDefinitionId(entity.getClaimDefinition() != null ? entity.getClaimDefinition().getId() : null);
        response.setClaimSetId(entity.getClaimSetClaimValue() != null ? entity.getClaimSetClaimValue().getClaimSetId() : null);
        response.setOwnerType(entity.getOwnerType());
        response.setOwnerId(entity.getOwnerId());
        response.setValue(parseValueForResponse(entity));
        response.setPrimary(entity.isPrimary());
        response.setComputed(entity.isComputed());
        response.setSource(entity.getSource());
        
        // Set audit information
        response.setAudit(new BaseResponse.AuditInfo()
                .setCreatedAt(entity.getCreatedAt())
                .setUpdatedAt(entity.getUpdatedAt())
                .setCreatedBy(entity.getCreatedBy())
                .setUpdatedBy(entity.getUpdatedBy()));

        return response;
    }

    /**
     * Parse the claim value based on the ClaimDefinition data type.
     * For ARRAY types, parse JSON string to actual array.
     * For other types, return the string value as-is.
     * 
     * @param entity the ClaimValue entity
     * @return parsed value (Object for arrays, String for others)
     */
    private Object parseValueForResponse(ClaimValueJpaEntity entity) {
        String rawValue = entity.getValue();
        if (rawValue == null) {
            return null;
        }

        // Get the ClaimDefinition to check its data type
        ClaimDefinitionJpaEntity claimDefinition = entity.getClaimDefinition();
        
        // If we can't access the claim definition or it's not ARRAY type, return as string
        if (claimDefinition == null || claimDefinition.getDataType() != DataTypeEnum.ARRAY) {
            return rawValue;
        }

        // For ARRAY type, try to parse JSON string to actual array
        try {
            return objectMapper.readValue(rawValue, Object[].class);
        } catch (JsonProcessingException e) {
            log.warn("Failed to parse ARRAY claim value as JSON for ClaimValue {}: {}. Returning as string.", 
                    entity.getId(), e.getMessage());
            return rawValue; // Fallback to string if JSON parsing fails
        }
    }
}
