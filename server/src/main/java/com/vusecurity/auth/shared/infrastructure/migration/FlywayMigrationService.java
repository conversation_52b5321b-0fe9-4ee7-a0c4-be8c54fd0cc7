package com.vusecurity.auth.shared.infrastructure.migration;

import javax.sql.DataSource;

import org.flywaydb.core.Flyway;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Executes Flyway migrations for a newly created tenant datasource.
 * We run this manually because auto-configuration for DataSource is disabled due to multitenancy.
 */
@Component
public class FlywayMigrationService {
    private static final Logger logger = LoggerFactory.getLogger(FlywayMigrationService.class);

    public void migrate(DataSource dataSource, String poolName) {
        logger.info("Starting Flyway migration for datasource ({})", poolName);
        Flyway flyway = Flyway.configure()
                .dataSource(dataSource)
                .locations("classpath:db/migration")
                .baselineOnMigrate(true)
                .failOnMissingLocations(false)
                .load();
        var result = flyway.migrate();
        try {
            int count = (int) result.getClass().getMethod("migrationsExecuted").invoke(result);
            logger.info("Flyway migration completed for datasource ({}). Applied: {} migrations", poolName, count);
        } catch (Exception reflectionError) {
            logger.info("Flyway migration completed for datasource ({}).", poolName);
        }
    }
}
