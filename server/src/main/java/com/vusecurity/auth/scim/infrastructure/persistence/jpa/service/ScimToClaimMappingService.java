package com.vusecurity.auth.scim.infrastructure.persistence.jpa.service;

import java.util.Collection;
import java.util.List;
import java.util.UUID;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.vusecurity.auth.scim.infrastructure.persistence.jpa.entity.ScimToClaimMappingJpaEntity;
import com.vusecurity.auth.scim.infrastructure.persistence.jpa.repository.ScimToClaimMappingRepository;

@Service
@Transactional
public class ScimToClaimMappingService {

    private final ScimToClaimMappingRepository scimToClaimMappingRepository;

    public ScimToClaimMappingService(ScimToClaimMappingRepository scimToClaimMappingRepository){
        this.scimToClaimMappingRepository = scimToClaimMappingRepository;
    }

    public List<ScimToClaimMappingJpaEntity> getByBusinessId(UUID businessId){
        return this.scimToClaimMappingRepository.findAllByBusinessId(businessId);
    }

    public ScimToClaimMappingJpaEntity get(UUID id){
        return this.scimToClaimMappingRepository.getById(id);
    }

    public void save(ScimToClaimMappingJpaEntity entity){
        this.scimToClaimMappingRepository.saveAndFlush(entity);
    }

    public void saveAll(Collection<ScimToClaimMappingJpaEntity> entityList){
        this.scimToClaimMappingRepository.saveAllAndFlush(entityList);
    }

}
