package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.command.CreateClaimValueCommand;
import com.vusecurity.auth.claims.application.command.UpdateClaimValueCommand;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetDefinitionMappingJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetDefinitionMappingRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimValueRepository;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import com.vusecurity.auth.contracts.enums.OwnerType;
import com.vusecurity.auth.identities.application.dto.AccountBusinessInfo;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UuidReferenceValidationTest {

    @Mock
    private ClaimValueRepository valueRepo;
    
    @Mock
    private ClaimDefinitionRepository defRepo;
    
    @Mock
    private ClaimSetDefinitionMappingRepository mappingRepo;
    
    @Mock
    private AccountRepository accountRepo;

    @InjectMocks
    private CreateClaimValueHandler createHandler;

    @InjectMocks
    private UpdateClaimValueHandler updateHandler;

    @Test
    void should_validate_array_with_uuid_reference_successfully() {
        // Given
        UUID phoneNumberDefId = UUID.fromString("2784994d-dc95-491b-8843-f5664dece55e");
        UUID phoneNumbersDefId = UUID.randomUUID();
        UUID accountId = UUID.randomUUID();
        UUID businessId = UUID.randomUUID();
        UUID claimSetId = UUID.randomUUID();

        ClaimSetJpaEntity claimSet = mock(ClaimSetJpaEntity.class);
        claimSet.setIsIdentifier(false);
        claimSet.setId(claimSetId);

        when(claimSet.getId()).thenReturn(claimSetId);
        when(claimSet.getIsIdentifier()).thenReturn(false);

        // Referenced ClaimDefinition (phone_number)
        ClaimDefinitionJpaEntity phoneNumberDef = new ClaimDefinitionJpaEntity(
                phoneNumberDefId, "phone_number", "Phone Number", "Individual phone number",
                DataTypeEnum.STRING, "^\\+?[1-9]\\d{1,14}$");

        // Array ClaimDefinition (phone_numbers) with UUID reference
        ClaimDefinitionJpaEntity phoneNumbersDef = new ClaimDefinitionJpaEntity(
                phoneNumbersDefId, "phone_numbers", "Phone Numbers", "List of phone numbers",
                DataTypeEnum.ARRAY, phoneNumberDefId.toString());
        
        ClaimSetDefinitionMappingJpaEntity mapping = new ClaimSetDefinitionMappingJpaEntity(claimSet, phoneNumbersDef);
        phoneNumbersDef.setClaimSetMappings(Set.of(mapping));
        claimSet.setClaimDefinitionMappings(Set.of(mapping));
        mapping.setEnforceUniqueness(false);

        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                claimSetId, phoneNumbersDefId, null, accountId, "[\"**********\", \"+**********\"]",
                true, false, "test");

        AccountBusinessInfo accountInfo = new AccountBusinessInfo(businessId, AccountType.CUSTOMER);

        when(defRepo.findById(phoneNumbersDefId)).thenReturn(Optional.of(phoneNumbersDef));
        when(defRepo.findById(phoneNumberDefId)).thenReturn(Optional.of(phoneNumberDef));
        when(accountRepo.findInfoById(accountId)).thenReturn(Optional.of(accountInfo));
        when(mappingRepo.findApplicableMappings(businessId, AccountType.CUSTOMER, phoneNumbersDefId))
                .thenReturn(List.of(mapping));
        when(valueRepo.saveAndFlush(any(ClaimValueJpaEntity.class))).thenAnswer(i -> i.getArguments()[0]);

        // When & Then - should not throw exception
        assertDoesNotThrow(() -> createHandler.handle(cmd));
    }

    @Test
    void should_fail_validation_when_array_element_doesnt_match_referenced_pattern() {
        // Given
        UUID phoneNumberDefId = UUID.fromString("2784994d-dc95-491b-8843-f5664dece55e");
        UUID phoneNumbersDefId = UUID.randomUUID();
        UUID claimSetId = UUID.randomUUID();

        ClaimSetJpaEntity claimSet = mock(ClaimSetJpaEntity.class);
        claimSet.setId(claimSetId);

        // Referenced ClaimDefinition (phone_number) with strict pattern
        ClaimDefinitionJpaEntity phoneNumberDef = new ClaimDefinitionJpaEntity(
                phoneNumberDefId, "phone_number", "Phone Number", "Individual phone number",
                DataTypeEnum.STRING, "^\\+?[1-9]\\d{1,14}$");

        // Array ClaimDefinition (phone_numbers) with UUID reference
        ClaimDefinitionJpaEntity phoneNumbersDef = new ClaimDefinitionJpaEntity(
                phoneNumbersDefId, "phone_numbers", "Phone Numbers", "List of phone numbers",
                DataTypeEnum.ARRAY, phoneNumberDefId.toString());

        ClaimSetDefinitionMappingJpaEntity mapping = new ClaimSetDefinitionMappingJpaEntity(claimSet ,phoneNumbersDef);
        phoneNumbersDef.setClaimSetMappings(Set.of(mapping));
        claimSet.setClaimDefinitionMappings(Set.of(mapping));

        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                claimSetId, phoneNumbersDefId, null, UUID.randomUUID(), "[\"**********\", \"invalid-phone\"]",
                true, false, "test");

        when(defRepo.findById(phoneNumbersDefId)).thenReturn(Optional.of(phoneNumbersDef));
        when(defRepo.findById(phoneNumberDefId)).thenReturn(Optional.of(phoneNumberDef));

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> createHandler.handle(cmd));
        
        assertTrue(exception.getMessage().contains("does not match the required pattern"));
        assertTrue(exception.getMessage().contains("invalid-phone"));
    }

    @Test
    void should_fail_validation_when_referenced_claim_definition_not_found() {
        // Given
        UUID phoneNumberDefId = UUID.fromString("2784994d-dc95-491b-8843-f5664dece55e");
        UUID phoneNumbersDefId = UUID.randomUUID();
        UUID claimSetId = UUID.randomUUID();

        ClaimSetJpaEntity claimSet = mock(ClaimSetJpaEntity.class);
        claimSet.setId(claimSetId);

        // Array ClaimDefinition (phone_numbers) with UUID reference
        ClaimDefinitionJpaEntity phoneNumbersDef = new ClaimDefinitionJpaEntity(
                phoneNumbersDefId, "phone_numbers", "Phone Numbers", "List of phone numbers",
                DataTypeEnum.ARRAY, phoneNumberDefId.toString());

        ClaimSetDefinitionMappingJpaEntity mapping = new ClaimSetDefinitionMappingJpaEntity(claimSet ,phoneNumbersDef);
        phoneNumbersDef.setClaimSetMappings(Set.of(mapping));
        claimSet.setClaimDefinitionMappings(Set.of(mapping));
        mapping.setEnforceUniqueness(false);

        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                claimSetId, phoneNumbersDefId, null, UUID.randomUUID(), "[\"**********\"]",
                true, false, "test");

        when(defRepo.findById(phoneNumbersDefId)).thenReturn(Optional.of(phoneNumbersDef));
        when(defRepo.findById(phoneNumberDefId)).thenReturn(Optional.empty()); // Referenced definition not found

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> createHandler.handle(cmd));
        
        assertTrue(exception.getMessage().contains("Referenced claim definition not found"));
    }

    @Test
    void should_validate_array_with_comma_separated_values() {
        // Given
        UUID emailDefId = UUID.fromString("beb4da69-6685-481b-a940-87823788b02a");
        UUID emailsDefId = UUID.randomUUID();
        UUID accountId = UUID.randomUUID();
        UUID businessId = UUID.randomUUID();
        UUID claimSetId = UUID.randomUUID();

        ClaimSetJpaEntity claimSet = mock(ClaimSetJpaEntity.class);
        claimSet.setId(claimSetId);
        claimSet.setIsIdentifier(false);

        when(claimSet.getId()).thenReturn(claimSetId);
        when(claimSet.getIsIdentifier()).thenReturn(false);

        // Referenced ClaimDefinition (email_address)
        ClaimDefinitionJpaEntity emailDef = new ClaimDefinitionJpaEntity(
                emailDefId, "email_address", "Email", "Email address",
                DataTypeEnum.STRING, "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");

        // Array ClaimDefinition (email_addresses) with UUID reference
        ClaimDefinitionJpaEntity emailsDef = new ClaimDefinitionJpaEntity(
                emailsDefId, "email_addresses", "Email Addresses", "List of email addresses",
                DataTypeEnum.ARRAY, emailDefId.toString());

        ClaimSetDefinitionMappingJpaEntity mapping = new ClaimSetDefinitionMappingJpaEntity(claimSet ,emailsDef);
        emailsDef.setClaimSetMappings(Set.of(mapping));
        claimSet.setClaimDefinitionMappings(Set.of(mapping));
        mapping.setEnforceUniqueness(false);

        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                claimSetId, emailsDefId, null, accountId, "<EMAIL>, <EMAIL>",
                true, false, "test");

        AccountBusinessInfo accountInfo = new AccountBusinessInfo(businessId, AccountType.CUSTOMER);

        when(defRepo.findById(emailsDefId)).thenReturn(Optional.of(emailsDef));
        when(defRepo.findById(emailDefId)).thenReturn(Optional.of(emailDef));
        when(accountRepo.findInfoById(accountId)).thenReturn(Optional.of(accountInfo));
        when(mappingRepo.findApplicableMappings(businessId, AccountType.CUSTOMER, emailsDefId))
                .thenReturn(List.of(mapping));
        when(valueRepo.saveAndFlush(any(ClaimValueJpaEntity.class))).thenAnswer(i -> i.getArguments()[0]);

        // When & Then - should not throw exception
        assertDoesNotThrow(() -> createHandler.handle(cmd));
    }

    @Test
    void should_fallback_to_regex_validation_for_non_uuid_dataformat() {
        // Given
        UUID arrayDefId = UUID.randomUUID();
        UUID accountId = UUID.randomUUID();
        UUID businessId = UUID.randomUUID();
        UUID claimSetId = UUID.randomUUID();

        ClaimSetJpaEntity claimSet = mock(ClaimSetJpaEntity.class);
        claimSet.setId(claimSetId);
        claimSet.setIsIdentifier(false);

        when(claimSet.getId()).thenReturn(claimSetId);
        when(claimSet.getIsIdentifier()).thenReturn(false);

        // Array ClaimDefinition with regex pattern (not UUID)
        ClaimDefinitionJpaEntity arrayDef = new ClaimDefinitionJpaEntity(
                arrayDefId, "simple_array", "Simple Array", "Array with regex validation",
                DataTypeEnum.ARRAY, "^\\[.*\\]$"); // Regex pattern, not UUID

        ClaimSetDefinitionMappingJpaEntity mapping = new ClaimSetDefinitionMappingJpaEntity(claimSet ,arrayDef);
        arrayDef.setClaimSetMappings(Set.of(mapping));
        claimSet.setClaimDefinitionMappings(Set.of(mapping));
        mapping.setEnforceUniqueness(false);

        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                claimSetId, arrayDefId, null, accountId, "[\"value1\", \"value2\"]",
                true, false, "test");

        AccountBusinessInfo accountInfo = new AccountBusinessInfo(businessId, AccountType.CUSTOMER);

        when(defRepo.findById(arrayDefId)).thenReturn(Optional.of(arrayDef));
        when(accountRepo.findInfoById(accountId)).thenReturn(Optional.of(accountInfo));
        when(mappingRepo.findApplicableMappings(businessId, AccountType.CUSTOMER, arrayDefId))
                .thenReturn(List.of(mapping));
        when(valueRepo.saveAndFlush(any(ClaimValueJpaEntity.class))).thenAnswer(i -> i.getArguments()[0]);

        // When & Then - should not throw exception (uses regex validation)
        assertDoesNotThrow(() -> createHandler.handle(cmd));
    }

    @Test
    void should_validate_non_array_types_with_regex_as_before() {
        // Given
        UUID stringDefId = UUID.randomUUID();
        UUID accountId = UUID.randomUUID();
        UUID businessId = UUID.randomUUID();
        UUID claimSetId = UUID.randomUUID();

        ClaimSetJpaEntity claimSet = mock(ClaimSetJpaEntity.class);
        claimSet.setId(claimSetId);
        claimSet.setIsIdentifier(false);

        when(claimSet.getId()).thenReturn(claimSetId);
        when(claimSet.getIsIdentifier()).thenReturn(false);

        // String ClaimDefinition with regex pattern
        ClaimDefinitionJpaEntity stringDef = new ClaimDefinitionJpaEntity(
                stringDefId, "email", "Email", "Email address",
                DataTypeEnum.STRING, "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");

        ClaimSetDefinitionMappingJpaEntity mapping = new ClaimSetDefinitionMappingJpaEntity(claimSet, stringDef);
        stringDef.setClaimSetMappings(Set.of(mapping));
        claimSet.setClaimDefinitionMappings(Set.of(mapping));
        mapping.setEnforceUniqueness(false);

        CreateClaimValueCommand cmd = new CreateClaimValueCommand(
                claimSetId, stringDefId, null, accountId, "<EMAIL>",
                true, false, "test");

        AccountBusinessInfo accountInfo = new AccountBusinessInfo(businessId, AccountType.CUSTOMER);
        
        when(defRepo.findById(stringDefId)).thenReturn(Optional.of(stringDef));
        when(accountRepo.findInfoById(accountId)).thenReturn(Optional.of(accountInfo));
        when(mappingRepo.findApplicableMappings(businessId, AccountType.CUSTOMER, stringDefId))
                .thenReturn(List.of(mapping));
        when(valueRepo.saveAndFlush(any(ClaimValueJpaEntity.class))).thenAnswer(i -> i.getArguments()[0]);

        // When & Then - should not throw exception
        assertDoesNotThrow(() -> createHandler.handle(cmd));
    }

    @Test
    void should_validate_update_claim_value_with_uuid_reference() {
        // Given
        UUID claimValueId = UUID.randomUUID();
        UUID phoneNumberDefId = UUID.fromString("2784994d-dc95-491b-8843-f5664dece55e");
        UUID phoneNumbersDefId = UUID.randomUUID();
        UUID claimSetId = UUID.randomUUID();

        ClaimSetJpaEntity claimSet = mock(ClaimSetJpaEntity.class);
        claimSet.setId(claimSetId);

        // Referenced ClaimDefinition (phone_number)
        ClaimDefinitionJpaEntity phoneNumberDef = new ClaimDefinitionJpaEntity(
                phoneNumberDefId, "phone_number", "Phone Number", "Individual phone number",
                DataTypeEnum.STRING, "^\\+?[1-9]\\d{1,14}$");

        // Array ClaimDefinition (phone_numbers) with UUID reference
        ClaimDefinitionJpaEntity phoneNumbersDef = new ClaimDefinitionJpaEntity(
                phoneNumbersDefId, "phone_numbers", "Phone Numbers", "List of phone numbers",
                DataTypeEnum.ARRAY, phoneNumberDefId.toString());

        ClaimSetDefinitionMappingJpaEntity mapping = new ClaimSetDefinitionMappingJpaEntity(claimSet, phoneNumbersDef);
        phoneNumbersDef.setClaimSetMappings(Set.of(mapping));
        claimSet.setClaimDefinitionMappings(Set.of(mapping));

        // Existing claim value
        ClaimValueJpaEntity existingClaimValue = new ClaimValueJpaEntity(phoneNumbersDef, OwnerType.ACCOUNT, UUID.randomUUID());
        existingClaimValue.setId(claimValueId);
        existingClaimValue.setClaimSetClaimValue(new ClaimSetClaimValueJpaEntity(claimSet, existingClaimValue));

        UpdateClaimValueCommand cmd = new UpdateClaimValueCommand(
                claimValueId, null, null, null, null, "[\"**********\", \"+**********\"]",
                null, null, null, null, null);

        when(valueRepo.findById(claimValueId)).thenReturn(Optional.of(existingClaimValue));
        when(defRepo.findById(phoneNumbersDefId)).thenReturn(Optional.of(phoneNumbersDef));
        when(defRepo.findById(phoneNumberDefId)).thenReturn(Optional.of(phoneNumberDef));
        when(valueRepo.saveAndFlush(any(ClaimValueJpaEntity.class))).thenAnswer(i -> i.getArguments()[0]);

        // When & Then - should not throw exception
        assertDoesNotThrow(() -> updateHandler.updateClaimValue(cmd));
    }
}