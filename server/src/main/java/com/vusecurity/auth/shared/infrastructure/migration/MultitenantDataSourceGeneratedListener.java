package com.vusecurity.auth.shared.infrastructure.migration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import com.vusecurity.auth.shared.infrastructure.migration.initial.ClaimsSetupService;
import com.vusecurity.auth.shared.infrastructure.migration.initial.OAuth2RegisteredClientSetupService;
import com.vusecurity.auth.shared.infrastructure.migration.initial.RolesAndPermissionsSetupService;
import com.vusecurity.auth.shared.infrastructure.migration.initial.SystemSetupService;
import com.vusecurity.multitenant.jpa.hibernate.events.DataSourceCreatedEvent;
import com.zaxxer.hikari.HikariDataSource;

@Component
public class MultitenantDataSourceGeneratedListener implements ApplicationListener<DataSourceCreatedEvent> {
    private static final Logger logger = LoggerFactory.getLogger(MultitenantDataSourceGeneratedListener.class);

    private final RolesAndPermissionsSetupService rolesAndPermissionSetupService;
    private final ClaimsSetupService claimsSetupService;
    private final SystemSetupService systemSetupService;
    private final OAuth2RegisteredClientSetupService oAuth2RegisteredClientSetupService;
    private final Environment environment;

    @Value("${app.dataseed.enabled:false}")
    private boolean dataSeedEnabled;

    public MultitenantDataSourceGeneratedListener(RolesAndPermissionsSetupService rolesAndPermissionSetupService,
            ClaimsSetupService claimsSetupService,
            SystemSetupService systemSetupService,
            OAuth2RegisteredClientSetupService oAuth2RegisteredClientSetupService,
            Environment environment) {
        this.systemSetupService = systemSetupService;
        this.claimsSetupService = claimsSetupService;
        this.rolesAndPermissionSetupService = rolesAndPermissionSetupService;
        this.oAuth2RegisteredClientSetupService = oAuth2RegisteredClientSetupService;
        this.environment = environment;
    }

    @Override
    public void onApplicationEvent(DataSourceCreatedEvent event) {
        String poolName = ((HikariDataSource) event.getDataSource()).getPoolName();
        logger.info("Data initialization request received for datasource ({})", poolName);

        // Always setup OAuth2 clients - critical for authorization server functionality
        try {
            oAuth2RegisteredClientSetupService.start();
            logger.info("OAuth2 registered client setup completed for datasource ({})", poolName);
        } catch (Exception e) {
            logger.error("OAuth2 client setup failed for datasource ({}): {}", poolName, e.getMessage(), e);
            throw e; // Critical failure - re-throw to prevent startup
        }

        // Security check: Verify if data seeding is explicitly enabled
        if (!isDataSeedingAllowed()) {
            logger.warn("Data seeding is DISABLED for datasource ({}). " +
                    "To enable, set app.dataseed.enabled=true or ENABLE_DATA_SEEDING=true environment variable.",
                    poolName);
            return;
        }

        logger.info("Data seeding is ENABLED for datasource ({}). Starting initialization...", poolName);

        try {
            // Initialize the database with default data
            systemSetupService.start();
            claimsSetupService.start();
            rolesAndPermissionSetupService.start();

            logger.info("Data initialization in datasource ({}) has completed successfully", poolName);
        } catch (Exception e) {
            logger.error("Data initialization in datasource ({}) failed with error: {}", poolName, e.getMessage(), e);
            throw e; // Re-throw to ensure the application is aware of the failure
        }
    }

    /**
     * Determines if data seeding is allowed based on configuration and environment
     * variables.
     */
    private boolean isDataSeedingAllowed() {
        // Check environment variable first (highest priority)
        String envEnabled = environment.getProperty("ENABLE_DATA_SEEDING");
        if (envEnabled != null) {
            boolean envEnabledFlag = Boolean.parseBoolean(envEnabled);
            logger.info("Data seeding control via ENABLE_DATA_SEEDING environment variable: {}", envEnabledFlag);
            return envEnabledFlag;
        }

        // Check application property
        logger.info("Data seeding control via app.dataseed.enabled property: {}", dataSeedEnabled);
        return dataSeedEnabled;
    }
}
