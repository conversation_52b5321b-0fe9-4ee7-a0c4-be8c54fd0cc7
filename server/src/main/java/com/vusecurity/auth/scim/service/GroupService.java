package com.vusecurity.auth.scim.service;

import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.directory.scim.core.repository.PatchHandler;
import org.apache.directory.scim.core.repository.Repository;
import org.apache.directory.scim.server.exception.UnableToCreateResourceException;
import org.apache.directory.scim.server.exception.UnableToRetrieveResourceException;
import org.apache.directory.scim.spec.exception.ResourceException;
import org.apache.directory.scim.spec.filter.*;
import org.apache.directory.scim.spec.filter.attribute.AttributeReference;
import org.apache.directory.scim.spec.patch.PatchOperation;
import org.apache.directory.scim.spec.resources.ScimExtension;
import org.apache.directory.scim.spec.resources.ScimGroup;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import org.springframework.context.annotation.Profile;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.vusecurity.auth.authorization.application.command.CreateGroupCommand;
import com.vusecurity.auth.authorization.application.command.DeleteGroupCommand;
import com.vusecurity.auth.authorization.application.command.UpdateGroupCommand;
import com.vusecurity.auth.authorization.application.exception.GroupNotFoundException;
import com.vusecurity.auth.authorization.application.handler.CreateGroupHandler;
import com.vusecurity.auth.authorization.application.handler.DeleteGroupHandler;
import com.vusecurity.auth.authorization.application.handler.UpdateGroupHandler;
import com.vusecurity.auth.authorization.application.query.GetGroupQuery;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.AccountGroupJpaEntity;
import com.vusecurity.auth.identities.application.query.GetAccountQuery;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountGroupRepository;
import com.vusecurity.auth.scim.mapper.FilterToSpecificationBuilder;
import com.vusecurity.auth.scim.mapper.GroupMapper;
import com.vusecurity.auth.shared.infrastructure.migration.initial.DataSeedConstants;

@Component
@Slf4j
@Profile("!test")
@RequiredArgsConstructor
public class GroupService implements Repository<ScimGroup> {

  private final PatchHandler patchHandler;

  private final GetGroupQuery getGroupQuery;

  private final CreateGroupHandler createGroupHandler;

  private final GetAccountQuery getAccountQuery;

  private final AccountGroupRepository accountGroupRepository;

  private final DeleteGroupHandler deleteGroupHandler;

  private final UpdateGroupHandler updateGroupHandler;

  @Override
  public Class<ScimGroup> getResourceClass() {
    return ScimGroup.class;
  }

  @Override
  public ScimGroup create(ScimGroup resource) throws UnableToCreateResourceException {
    // if the external ID is not set, use the displayName instead
    if (!StringUtils.hasText(resource.getExternalId())) {
      resource.setExternalId(resource.getDisplayName());
    }

    AccountGroupJpaEntity newGroup = GroupMapper.fromGroupWithoutMembers(resource);

    if (getGroupQuery.findGroupByName(newGroup.getName()).isPresent()){
        throw new UnableToCreateResourceException(Status.CONFLICT, "Group '" + newGroup.getName() + "' already exists.");
    }

    List<UUID> directMemberList;
    try {
        directMemberList = GroupMapper.groupMembershipToUUIDs(resource.getMembers());
    } catch (Exception e){
        throw new UnableToCreateResourceException(Status.BAD_REQUEST, "Unable to parse members ref to UUIDs - " + e.getMessage());
    }


    //TODO: Get the correct Business
    //Get the IDs that belong to the correct business even if the createGroupHandler already check for them to exists.
    directMemberList = getAccountQuery.filterUUIDsByBusiness(DataSeedConstants.SYSTEM_BUSINESS_ID, directMemberList);

    CreateGroupCommand createGroupCommand = new CreateGroupCommand(newGroup.getName(), newGroup.getDescription(), null, directMemberList, null, newGroup.getMetadata());

    try {
        newGroup = createGroupHandler.createGroup(createGroupCommand);
    } catch (Exception e){
        log.error("GroupsService - create - createGroupError - {}", e.getMessage());
        throw new UnableToCreateResourceException(Response.Status.INTERNAL_SERVER_ERROR, "Failed to create group '" + resource.getExternalId() + "' cause: " + e.getMessage());
    }

    return GroupMapper.toGroup(newGroup);
  }

  @Override
  public ScimGroup update(String id, String version, ScimGroup resource, Set<AttributeReference> includedAttributeReferences, Set<AttributeReference> excludedAttributeReferences) throws ResourceException {
    AccountGroupJpaEntity group;
    UUID groupId;
    try {
        groupId = UUID.fromString(id);
        getGroupQuery.getActiveGroupById(groupId);
    } catch (GroupNotFoundException e){
        throw new UnableToRetrieveResourceException(Status.NOT_FOUND, e.getMessage());
    } catch (Exception e){
        throw new ResourceException(500, e.getMessage());
    }
    group = GroupMapper.fromGroupWithoutMembers(resource);
    List<UUID> directMembers = GroupMapper.groupMembershipToUUIDs(resource.getMembers());
    //Remove any duplicated values that may exist.
    directMembers = directMembers.stream().collect(Collectors.toSet()).stream().toList();

    UpdateGroupCommand updateGroupCommand = new UpdateGroupCommand(groupId, group.getName(), group.getDescription(), null, directMembers, null, group.getMetadata());
    AccountGroupJpaEntity updatedGroup;
    try{
        updatedGroup = updateGroupHandler.updateGroup(updateGroupCommand);
    } catch (Exception e){
        throw new ResourceException(500, e.getMessage());
    }

    return GroupMapper.toGroup(updatedGroup);
  }

  @Override
  public ScimGroup patch(String id, String version, List<PatchOperation> patchOperations, Set<AttributeReference> includedAttributeReferences, Set<AttributeReference> excludedAttributeReferences) throws ResourceException {
    ScimGroup originalGroup = get(id);
    ScimGroup patchedGroup = patchHandler.apply(originalGroup, patchOperations);

    return this.update(id, version, patchedGroup, includedAttributeReferences, excludedAttributeReferences);
  }

  @Override
  public ScimGroup get(String id) throws ResourceException {
    AccountGroupJpaEntity group;
    try {
        group = getGroupQuery.getActiveGroupById(UUID.fromString(id));
    } catch (GroupNotFoundException e){
        throw new UnableToRetrieveResourceException(Status.NOT_FOUND, e.getMessage());
    } catch (Exception e){
        throw new ResourceException(500, e.getMessage());
    }

    return GroupMapper.toGroup(group);
  }

  @Override
  public void delete(String id) throws ResourceException {
    try {
        DeleteGroupCommand deleteCommand = new DeleteGroupCommand(UUID.fromString(id));
        deleteGroupHandler.deleteGroup(deleteCommand);
    } catch (GroupNotFoundException e){
        //Taken from the ms scim validation tool, future version may required to return NOT_FOUND
        throw new UnableToRetrieveResourceException(Status.NOT_FOUND, e.getMessage());
    } catch (Exception e){
        throw new ResourceException(500, e.getMessage());
    }
  }

  @Override
  public FilterResponse<ScimGroup> find(Filter filter, PageRequest pageRequest, SortRequest sortRequest) {
    int count = pageRequest.getCount() != null ? pageRequest.getCount() : 100;
    int startIndex = pageRequest.getStartIndex() != null
      ? pageRequest.getStartIndex() - 1
      : 0;

    Specification<AccountGroupJpaEntity> spec = null;
    String sortAttribute = "id";
    Sort.Direction direction = Sort.Direction.ASC;
    if (sortRequest.getSortBy() != null){
        direction = sortRequest.getSortOrder().equals(SortOrder.ASCENDING) ? Sort.Direction.ASC : Sort.Direction.DESC;
    }
    if (sortRequest.getSortOrder() != null){
        sortAttribute = GroupMapper.groupMapping.getOrDefault(sortRequest.getSortBy().getFullAttributeName(), List.of("id")).get(0);
    }
    Sort sort = Sort.by(direction, sortAttribute);

    org.springframework.data.domain.PageRequest pageable = org.springframework.data.domain.PageRequest.of(startIndex, count, sort);
    
    if (filter != null){
        spec = new FilterToSpecificationBuilder<AccountGroupJpaEntity>(GroupMapper.groupMapping).build(filter);
    }

    List<ScimGroup> result = new ArrayList<>();

    Page<AccountGroupJpaEntity> pagedFilter = this.accountGroupRepository.findAll(spec, pageable);
    if (!pagedFilter.isEmpty()){
        result.addAll(pagedFilter.stream().map(GroupMapper::toGroup).toList());
    }

    return new FilterResponse<>(result, pageRequest, result.size());
  }

  @Override
  public List<Class<? extends ScimExtension>> getExtensionList() {
    return Collections.emptyList();
  }

}
