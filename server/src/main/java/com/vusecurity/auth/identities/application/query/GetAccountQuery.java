package com.vusecurity.auth.identities.application.query;

import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import org.springframework.data.domain.Page;

public interface GetAccountQuery {
    AccountJpaEntity getAccountById(String accountId);
    Page<AccountJpaEntity> getAllAccounts(GetAccountsPagedQuery query);
    Page<AccountJpaEntity> getAccountsByIdentity(GetAccountsByIdentityQuery query);
}
