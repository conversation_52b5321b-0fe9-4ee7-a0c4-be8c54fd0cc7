/*
 * Flyway Migration: V3__add_factor_permissions.sql
 * Purpose: Introduce factor (TOTP, SMS, Email, Password, Policies) permissions and assign them to existing roles.
 * Idempotent: Uses IF NOT EXISTS checks for SQL Server syntax.
 */

-- Factor TOTP permissions
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = 'a29e8a6e-9d50-434a-857e-6c111c8172c9') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('a29e8a6e-9d50-434a-857e-6c111c8172c9','sys_factor_totp:create','Allow to create TOTP factors','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = '46b76be4-a78e-4d94-9f94-53e51c8502d0') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('46b76be4-a78e-4d94-9f94-53e51c8502d0','sys_factor_totp:read','Allow to read TOTP factors','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = 'd01ac2ad-120f-4272-96b6-e280f6345165') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('d01ac2ad-120f-4272-96b6-e280f6345165','sys_factor_totp:update','Allow to update TOTP factors','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = '2b76862c-3d48-497c-8c2e-24fb3d66da00') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('2b76862c-3d48-497c-8c2e-24fb3d66da00','sys_factor_totp:delete','Allow to delete TOTP factors','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = '28657e33-e179-4ced-8a92-457fce997fae') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('28657e33-e179-4ced-8a92-457fce997fae','sys_factor_totp:all','Allow all TOTP factor operations','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());

-- Factor SMS permissions
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = 'adeb7443-b028-4564-992b-3edaa9a28452') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('adeb7443-b028-4564-992b-3edaa9a28452','sys_factor_sms:create','Allow to create SMS factors','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = 'df4a93eb-466a-4217-bef5-0c2aafe3adeb') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('df4a93eb-466a-4217-bef5-0c2aafe3adeb','sys_factor_sms:read','Allow to read SMS factors','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = 'd80aec72-f433-4259-bbf5-00b587ff90d9') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('d80aec72-f433-4259-bbf5-00b587ff90d9','sys_factor_sms:update','Allow to update SMS factors','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = '38720e22-ea55-4a81-b819-2eca45202db1') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('38720e22-ea55-4a81-b819-2eca45202db1','sys_factor_sms:delete','Allow to delete SMS factors','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = '1a36d1d6-7eba-427a-b097-4e6e881fb558') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('1a36d1d6-7eba-427a-b097-4e6e881fb558','sys_factor_sms:all','Allow all SMS factor operations','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());

-- Factor Email permissions
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = '365c9b68-5ea9-4912-b6b4-588923143b60') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('365c9b68-5ea9-4912-b6b4-588923143b60','sys_factor_email:create','Allow to create Email factors','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = '3e9da38f-6fb2-431c-9666-a89286ec6e61') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('3e9da38f-6fb2-431c-9666-a89286ec6e61','sys_factor_email:read','Allow to read Email factors','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = '446bf8b8-3abd-414e-89b0-49fbae82f3d8') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('446bf8b8-3abd-414e-89b0-49fbae82f3d8','sys_factor_email:update','Allow to update Email factors','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = 'a95d38f1-07ef-4d88-8b8c-36c86d9ad7c2') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('a95d38f1-07ef-4d88-8b8c-36c86d9ad7c2','sys_factor_email:delete','Allow to delete Email factors','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = '6cc3d3f8-5cd9-4372-ab55-841e4f047074') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('6cc3d3f8-5cd9-4372-ab55-841e4f047074','sys_factor_email:all','Allow all Email factor operations','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());

-- Factor Password permissions
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = '83ff93f4-1879-4e5d-8f1a-6ffff7d92800') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('83ff93f4-1879-4e5d-8f1a-6ffff7d92800','sys_factor_password:create','Allow to create Password factors','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = 'cdc88324-602e-4461-ba65-79d6f5aa9727') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('cdc88324-602e-4461-ba65-79d6f5aa9727','sys_factor_password:read','Allow to read Password factors','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = '38febaf5-e637-40a9-b42f-631978980b05') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('38febaf5-e637-40a9-b42f-631978980b05','sys_factor_password:update','Allow to update Password factors','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = 'a5a482be-0142-420e-822a-f4091d22b236') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('a5a482be-0142-420e-822a-f4091d22b236','sys_factor_password:delete','Allow to delete Password factors','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = 'ac02fd0f-fdbd-4295-bee2-ddd49a2335d2') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('ac02fd0f-fdbd-4295-bee2-ddd49a2335d2','sys_factor_password:all','Allow all Password factor operations','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());

-- Factor Policies permissions
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = '4de0b8dd-3e0c-4055-9177-c13d901f18f1') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('4de0b8dd-3e0c-4055-9177-c13d901f18f1','sys_factor_policies:create','Allow to create Factor policies','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = '4212c99e-de64-4e6e-9478-5655c53e9b18') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('4212c99e-de64-4e6e-9478-5655c53e9b18','sys_factor_policies:read','Allow to read Factor policies','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = 'd78738b0-50f2-44ec-9c4a-3bf0d712016e') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('d78738b0-50f2-44ec-9c4a-3bf0d712016e','sys_factor_policies:update','Allow to update Factor policies','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = '1c98dfd4-57ca-4045-b5a0-022cb251af87') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('1c98dfd4-57ca-4045-b5a0-022cb251af87','sys_factor_policies:delete','Allow to delete Factor policies','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());
IF NOT EXISTS (SELECT 1 FROM permission WHERE id = '4bc58115-4c53-43d7-b168-009f75896f5a') INSERT INTO permission (id, name, description, created_by, updated_by, created_at, updated_at) VALUES ('4bc58115-4c53-43d7-b168-009f75896f5a','sys_factor_policies:all','Allow all Factor policies operations','SYSTEM','SYSTEM',SYSUTCDATETIME(),SYSUTCDATETIME());

-- Assign to ADMIN (all new factor permissions)
INSERT INTO role_permission (role_id, permission_name)
SELECT '9fa7a7f6-b8d4-4db4-a8c7-1642a80e863a', p.id FROM permission p
WHERE p.name LIKE 'sys_factor_%'
AND NOT EXISTS (
    SELECT 1 FROM role_permission rp WHERE rp.role_id = '9fa7a7f6-b8d4-4db4-a8c7-1642a80e863a' AND rp.permission_name = p.id
);

-- Assign only read to OPERATOR for factors
-- TOTP read
IF NOT EXISTS (SELECT 1 FROM role_permission WHERE role_id = '779f520a-bb5f-45ef-95b8-3f2ea204023d' AND permission_name = '46b76be4-a78e-4d94-9f94-53e51c8502d0')
    INSERT INTO role_permission (role_id, permission_name) VALUES ('779f520a-bb5f-45ef-95b8-3f2ea204023d','46b76be4-a78e-4d94-9f94-53e51c8502d0');
-- SMS read
IF NOT EXISTS (SELECT 1 FROM role_permission WHERE role_id = '779f520a-bb5f-45ef-95b8-3f2ea204023d' AND permission_name = 'df4a93eb-466a-4217-bef5-0c2aafe3adeb')
    INSERT INTO role_permission (role_id, permission_name) VALUES ('779f520a-bb5f-45ef-95b8-3f2ea204023d','df4a93eb-466a-4217-bef5-0c2aafe3adeb');
-- Email read
IF NOT EXISTS (SELECT 1 FROM role_permission WHERE role_id = '779f520a-bb5f-45ef-95b8-3f2ea204023d' AND permission_name = '3e9da38f-6fb2-431c-9666-a89286ec6e61')
    INSERT INTO role_permission (role_id, permission_name) VALUES ('779f520a-bb5f-45ef-95b8-3f2ea204023d','3e9da38f-6fb2-431c-9666-a89286ec6e61');
-- Password read
IF NOT EXISTS (SELECT 1 FROM role_permission WHERE role_id = '779f520a-bb5f-45ef-95b8-3f2ea204023d' AND permission_name = 'cdc88324-602e-4461-ba65-79d6f5aa9727')
    INSERT INTO role_permission (role_id, permission_name) VALUES ('779f520a-bb5f-45ef-95b8-3f2ea204023d','cdc88324-602e-4461-ba65-79d6f5aa9727');
-- Policies read
IF NOT EXISTS (SELECT 1 FROM role_permission WHERE role_id = '779f520a-bb5f-45ef-95b8-3f2ea204023d' AND permission_name = '4212c99e-de64-4e6e-9478-5655c53e9b18')
    INSERT INTO role_permission (role_id, permission_name) VALUES ('779f520a-bb5f-45ef-95b8-3f2ea204023d','4212c99e-de64-4e6e-9478-5655c53e9b18');
