package com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity;

import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.shared.infrastructure.persistence.jpa.AbstractEntity;
import jakarta.persistence.*;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;

@Entity
@Table(name = "role",
        indexes = {
                @Index(name = "uk_role_name",        columnList = "name",        unique = true),
                @Index(name = "uk_role_id",          columnList = "id",          unique = true),
                @Index(name = "idx_role_business",   columnList = "business_id")
        })
public class RoleJpaEntity extends AbstractEntity {

    @Column(nullable = false, unique = true, length = 255)
    private String name;

    @Column(length = 1000)
    private String description;

    private UUID businessId;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "role_permission",
            joinColumns = @JoinColumn(name = "role_id"),
            inverseJoinColumns = @JoinColumn(name = "permission_name")
    )
    private Set<PermissionJpaEntity> permissions = new HashSet<>();

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "account_role",
            joinColumns = @JoinColumn(
                    name = "role_id",
                    foreignKey = @ForeignKey(name = "fk_account_role_role")
            ),
            inverseJoinColumns = @JoinColumn(
                    name = "account_id",
                    foreignKey = @ForeignKey(name = "fk_account_role_account")
            )
    )
    private Set<AccountJpaEntity> accounts = new HashSet<>();

    // Constructors
    public RoleJpaEntity() {
    }

    // Constructor with ID (for migration/test scenarios)
    public RoleJpaEntity(UUID id, UUID businessId, String name, String description) {
        this.setId(id);
        this.businessId = businessId;
        this.name = name;
        this.description = description;
        this.permissions = new HashSet<>();
        this.accounts = new HashSet<>();
    }

    // Constructor without ID (for production use - JPA will generate ID)
    public RoleJpaEntity(UUID businessId, String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("name cannot be null or empty");
        }

        this.businessId = businessId;
        this.name = name.trim();
        this.permissions = new HashSet<>();
        this.accounts = new HashSet<>();
    }

    // Getters
    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public UUID getBusinessId() {
        return businessId;
    }

    public Set<PermissionJpaEntity> getPermissions() {
        return permissions != null ? new HashSet<>(permissions) : new HashSet<>();
    }

    public Set<AccountJpaEntity> getAccounts() {
        return accounts != null ? new HashSet<>(accounts) : new HashSet<>();
    }

    // Setters
    public void setName(String name) {
        this.name = name;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public void setBusinessId(UUID businessId) {
        this.businessId = businessId;
    }

    public void setPermissions(Set<PermissionJpaEntity> permissions) {
        this.permissions = permissions != null ? new HashSet<>(permissions) : new HashSet<>();
    }

    public void setAccounts(Set<AccountJpaEntity> accounts) {
        this.accounts = accounts != null ? new HashSet<>(accounts) : new HashSet<>();
    }

    // Business behavior methods
    public void changeDescription(String newDescription) {
        this.description = newDescription;
    }

    public void addPermission(PermissionJpaEntity permission) {
        if (permission != null) {
            this.permissions.add(permission);
            permission.getRoles().add(this);
        }
    }

    public void removePermission(PermissionJpaEntity permission) {
        if (permission != null) {
            this.permissions.remove(permission);
            permission.getRoles().remove(this);
        }
    }

    public boolean hasPermission(String permissionName) {
        return permissions.stream()
                .anyMatch(permission -> permissionName.equals(permission.getName()));
    }

    // equals() and hashCode() using name (unique business key)
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        RoleJpaEntity that = (RoleJpaEntity) o;
        return Objects.equals(name, that.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), name);
    }

    @Override
    public String toString() {
        return "RoleJpaEntity{" +
                "id=" + getId() +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", businessId=" + businessId +
                '}';
    }
}
