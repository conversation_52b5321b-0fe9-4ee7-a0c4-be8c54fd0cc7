package com.vusecurity.auth.shared.infrastructure.migration.initial;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimValueRepository;
import com.vusecurity.auth.contracts.api.v1.dto.shared.SetupAdminUserResponse;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.contracts.enums.IdentityType;
import com.vusecurity.auth.contracts.enums.OwnerType;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.IdentityProviderJpaEntity;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.IdentityProviderRepository;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.IdentityRepository;
import com.vusecurity.auth.shared.infrastructure.security.mfa.FactorServerClient;
import com.vusecurity.business.domain.Business;
import com.vusecurity.business.domain.repositories.BusinessRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.security.SecureRandom;
import java.util.Optional;
import java.util.UUID;

@Component
public class SetupAdminUserService {
    private static final Logger logger = LoggerFactory.getLogger(SetupAdminUserService.class);

    private final AccountRepository accountRepository;
    private final IdentityRepository identityRepository;
    private final BusinessRepository businessRepository;
    private final IdentityProviderRepository identityProviderRepository;
    private final ClaimDefinitionRepository claimDefinitionRepository;
    private final ClaimValueRepository claimValueRepository;
    private final FactorServerClient factorServerClient;

    public SetupAdminUserService(AccountRepository accountRepository, IdentityRepository identityRepository, BusinessRepository businessRepository, IdentityProviderRepository identityProviderRepository, ClaimDefinitionRepository claimDefinitionRepository, ClaimValueRepository claimValueRepository, FactorServerClient factorServerClient) {
        this.accountRepository = accountRepository;
        this.identityRepository = identityRepository;
        this.businessRepository = businessRepository;
        this.identityProviderRepository = identityProviderRepository;
        this.claimDefinitionRepository = claimDefinitionRepository;
        this.claimValueRepository = claimValueRepository;
        this.factorServerClient = factorServerClient;
    }

    public SetupAdminUserResponse start() {
        logger.info("SetupAdminUserService: Starting setup admin user");
        logger.trace("Entering start method.");

        IdentityProviderJpaEntity identityProvider = identityProviderRepository.getReferenceById(DataSeedConstants.IDENTITY_PROVIDER_ID);
        Business business = businessRepository.getReferenceById(DataSeedConstants.SYSTEM_BUSINESS_ID);
        UUID identityId = DataSeedConstants.ADMIN_IDENTITY_ID;
        UUID accountId = DataSeedConstants.ADMIN_ACCOUNT_ID;

        Optional<IdentityJpaEntity> optionalIdentity = identityRepository.findByIdentityId(identityId);

        IdentityJpaEntity identity;
        if (optionalIdentity.isEmpty()) {
            identity = new IdentityJpaEntity(
                    identityId,
                    IdentityType.PERSON,
                    "ADMIN"
            );
            identity.activate();
            identity.setCreatedBy("SYSTEM");
            identityRepository.save(identity);
            logger.info("Identity created and saved with ID: {}", identity.getId());
        } else {
            identity =  optionalIdentity.get();
            logger.info("Identity already exist in the repository. Skipping identity setup.");
        }

        Optional<AccountJpaEntity> optionalAccount = accountRepository.findById(accountId);

        AccountJpaEntity account = null;

        if (optionalAccount.isEmpty()) {

            account = new AccountJpaEntity(
                    DataSeedConstants.ADMIN_ACCOUNT_ID,
                    business,
                    identity,
                    identityProvider,
                    AccountType.WORKFORCE
            );
            account.activate();
            account.setCreatedBy("SYSTEM");
            accountRepository.save(account);

            logger.info("Account created and saved with ID: {}", account.getId());
            logger.info("SetupAdminUserService: Admin user created successfully");
        } else {
            logger.info("Account already exist in the repository. Skipping account setup.");
        }

        String adminEmail = "<EMAIL>";
        String adminPassword = generatePassword();

        addEmailClaimValue(account, adminEmail);

        try {
            assert account != null;
            addPasswordClaimValue(account, adminPassword);
        } catch (Exception e) {
            logger.info("Error creating password. Skipping password setup.");
            return new SetupAdminUserResponse(null, null, null);
        }

        return new SetupAdminUserResponse(account.getId().toString(), adminEmail, adminPassword);
    }

    private String generatePassword() {
        String uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        String lowercase = "abcdefghijklmnopqrstuvwxyz";
        String numbers = "**********";
        String specialChars = "!@#$%";
        String allChars = uppercase + lowercase + numbers + specialChars;

        SecureRandom random = new SecureRandom();
        StringBuilder password = new StringBuilder();

        // Ensure at least one character from each required category
        password.append(uppercase.charAt(random.nextInt(uppercase.length())));
        password.append(lowercase.charAt(random.nextInt(lowercase.length())));
        password.append(numbers.charAt(random.nextInt(numbers.length())));
        password.append(specialChars.charAt(random.nextInt(specialChars.length())));

        // Fill the remaining positions (12 - 4 = 8) with random characters from all categories
        for (int i = 4; i < 12; i++) {
            password.append(allChars.charAt(random.nextInt(allChars.length())));
        }

        // Shuffle the password to avoid predictable patterns
        char[] passwordArray = password.toString().toCharArray();
        for (int i = passwordArray.length - 1; i > 0; i--) {
            int j = random.nextInt(i + 1);
            char temp = passwordArray[i];
            passwordArray[i] = passwordArray[j];
            passwordArray[j] = temp;
        }

        return new String(passwordArray);
    }

    private void addEmailClaimValue(AccountJpaEntity account, String email) {
        claimDefinitionRepository.findByCode("email_address")
                .ifPresent(claimDef -> {
                    ClaimValueJpaEntity emailClaim =  new ClaimValueJpaEntity(
                            claimDef, OwnerType.ACCOUNT, account.getId());
                    emailClaim.setValue(email);
                    emailClaim.setPrimary(true);
                    emailClaim.setSource("USER_INPUT");
                    emailClaim.setCreatedBy("SYSTEM");
                    claimValueRepository.save(emailClaim);
                });
    }

    private void addPasswordClaimValue(AccountJpaEntity account, String password){
        factorServerClient.createPassword(account.getId(),password,account.getBusiness().getId().toString());
    }
}
