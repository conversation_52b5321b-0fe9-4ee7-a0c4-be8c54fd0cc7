package com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity;

import com.vusecurity.auth.shared.infrastructure.persistence.jpa.AbstractEntity;
import com.vusecurity.core.commons.utils.converters.MapToJsonConverter;

import jakarta.persistence.*;
import jakarta.validation.constraints.Size;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;

@Entity
@Table(name = "account_group",
        indexes = {
                @Index(name = "uk_account_group_name", columnList = "name", unique = true),
                @Index(name = "uk_account_group_id",   columnList = "id",   unique = true)
        })
public class AccountGroupJpaEntity extends AbstractEntity {

    @Column(nullable = false, unique = true)
    private String name;

    @Column(length = 1024)
    @Size(max = 1024)
    private String description;

    @Convert(converter = MapToJsonConverter.class)
    private Map<String, Object> metadata = new HashMap<>();

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "account_group_role",
            joinColumns = @JoinColumn(
                    name = "account_group_id",
                    referencedColumnName = "id",
                    foreignKey = @ForeignKey(name = "fk_account_group_role_group")
            ),
            inverseJoinColumns = @JoinColumn(
                    name = "role_id",
                    referencedColumnName = "id",
                    foreignKey = @ForeignKey(name = "fk_account_group_role_role")
            )
    )
    private Set<RoleJpaEntity> roles = new HashSet<>();

    // Constructors
    public AccountGroupJpaEntity() {
    }

    public AccountGroupJpaEntity(String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("name cannot be null or empty");
        }
        this.name = name.trim();
        this.metadata = new HashMap<>();
        this.roles = new HashSet<>();
    }

    public AccountGroupJpaEntity(UUID id, String name) {
        this(name);
        this.setId(id);
    }

    // Getters
    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public Map<String, Object> getMetadata() {
        return metadata != null ? new HashMap<>(metadata) : new HashMap<>();
    }

    public Set<RoleJpaEntity> getRoles() {
        return roles != null ? new HashSet<>(roles) : new HashSet<>();
    }

    // Setters
    public void setName(String name) {
        this.name = name;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata != null ? new HashMap<>(metadata) : new HashMap<>();
    }

    public void setRoles(Set<RoleJpaEntity> roles) {
        this.roles = roles != null ? new HashSet<>(roles) : new HashSet<>();
    }

    // equals() and hashCode() using name (unique business key)
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        AccountGroupJpaEntity that = (AccountGroupJpaEntity) o;
        return Objects.equals(name, that.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), name);
    }

    @Override
    public String toString() {
        return "AccountGroupJpaEntity{" +
                "id=" + getId() +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                '}';
    }
}