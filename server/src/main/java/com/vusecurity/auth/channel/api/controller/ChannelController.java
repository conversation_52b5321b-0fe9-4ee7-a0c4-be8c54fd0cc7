package com.vusecurity.auth.channel.api.controller;

import com.vusecurity.channel.commons.api.model.enums.ChannelType;
import com.vusecurity.channel.commons.api.model.request.CreateChannelRequest;
import com.vusecurity.channel.commons.api.model.request.UpdateChannelRequest;
import com.vusecurity.channel.commons.api.model.response.ChannelResponse;
import com.vusecurity.channel.domain.Channel;
import com.vusecurity.channel.usecases.interfaces.ICreateChannel;
import com.vusecurity.channel.usecases.interfaces.IRetrieveChannel;
import com.vusecurity.channel.usecases.interfaces.IUpdateChannel;
import com.vusecurity.core.commons.models.PageableResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

@RestController
@SecurityRequirement(name = "oauth2 client credentials")
@Tag(name = "Channel", description = "Channel management operations")
public class ChannelController {

    private static final String SYSTEM_USER = "SYSTEM";

    private final IRetrieveChannel retrieveChannel;
    private final ICreateChannel createChannel;
    private final IUpdateChannel updateChannel;

    public ChannelController(IRetrieveChannel retrieveChannel, ICreateChannel createChannel, IUpdateChannel updateChannel) {
        this.retrieveChannel = retrieveChannel;
        this.createChannel = createChannel;
        this.updateChannel = updateChannel;
    }

    @Operation(
            summary = "Get all channels",
            description = "Retrieves a paginated list of channels with optional filtering by channel type. Channel types include WEB, MOBILE_APP, ATM, POS, API, ON_SITE, and AUTOMATION."
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Successful retrieval",
                    content = @Content(schema = @Schema(implementation = PageableResponse.class))),
            @ApiResponse(responseCode = "400", description = "Invalid request parameters"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("${app.business.context-path}/channels")
    public PageableResponse<ChannelResponse> get(
            @RequestParam(name = "page", defaultValue = "1") int page,
            @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
            @RequestParam(name = "channelType", required = false) String channelType) {
        Page<Channel> channels =  retrieveChannel.retrieveChannelsAll(page, pageSize, channelType);

        return new PageableResponse<>(channels.getNumber() + 1, channels.getSize(), channels.getTotalElements(), channels.map(ChannelController::getChannelResponse).toList());
    }

    @Operation(
            summary = "Get all no deleted channels",
            description = "Retrieves a paginated list of available channels with optional filtering by channel type. Channel types include WEB, MOBILE_APP, ATM, POS, API, ON_SITE, and AUTOMATION."
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Successful retrieval",
                    content = @Content(schema = @Schema(implementation = PageableResponse.class))),
            @ApiResponse(responseCode = "400", description = "Invalid request parameters"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("${app.business.context-path}/channels/no-deleted")
    public PageableResponse<ChannelResponse> getNoDeleted(
            @RequestParam(name = "page", defaultValue = "1") int page,
            @RequestParam(name = "pageSize", defaultValue = "10") int pageSize) {
        Page<Channel> channels =  retrieveChannel.retrieveAllChannelsNoDeleted(page, pageSize);

        return new PageableResponse<>(channels.getNumber() + 1, channels.getSize(), channels.getTotalElements(), channels.map(ChannelController::getChannelResponse).toList());
    }

    @Operation(
            summary = "Create a new channel",
            description = "Allows you to create a new channel with its corresponding details. The name is required and must be unique"
    )
    @ApiResponse(responseCode = "201", description = "Channel created successfully", content = {@Content(mediaType = "application/json", schema = @Schema(implementation = ChannelResponse.class))})
    @ApiResponse(responseCode = "400", description = "Incorrect request. Some attribute sent is invalid")
    @ApiResponse(responseCode = "409", description = "A unique field is already registered in another entity")
    @PostMapping("${app.business.context-path}/channels")
    @ResponseStatus(HttpStatus.CREATED)
    public ChannelResponse create(@Valid @RequestBody CreateChannelRequest request) {
        Channel channel = createChannel.createChannel(
                request.getName(),
                request.getDescription(),
                request.getChannelType(),
                request.getMetadata(),
                SYSTEM_USER
        );

        return getChannelResponse(channel);
    }

    @Operation(
            summary = "Get a channel by id",
            description = "Retrieves a single channel by its id"
    )
    @ApiResponse(responseCode = "200", description = "Successful retrieval", content = {@Content(mediaType = "application/json", schema = @Schema(implementation = ChannelResponse.class))})
    @ApiResponse(responseCode = "404", description = "Channel not found")
    @GetMapping("${app.business.context-path}/channels/{channelId}")
    public ChannelResponse get(@PathVariable(value = "channelId") UUID channelId) {
        Channel channel = retrieveChannel.retrieveChannelById(channelId);

        return getChannelResponse(channel);
    }

    @Operation(
            summary = "Partially update a channel",
            description = "Allows you to update one or more properties of a channel by its id"
    )
    @ApiResponse(responseCode = "204", description = "Channel updated successfully")
    @ApiResponse(responseCode = "400", description = "Validation error")
    @ApiResponse(responseCode = "404", description = "Channel not found")
    @ApiResponse(responseCode = "409", description = "A unique field is already registered in another entity")
    @PatchMapping("${app.business.context-path}/channels/{channelId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void update(@PathVariable(value = "channelId") UUID channelId, @Valid @RequestBody UpdateChannelRequest request) {
        updateChannel.updateChannel(
                channelId,
                request.getName(),
                request.getDescription(),
                request.getChannelType(),
                request.getMetadata(),
                request.getStatus(),
                SYSTEM_USER
        );
    }

    @Operation(
            summary = "Delete a channel",
            description = "Allows you to delete a channel by its id"
    )
    @ApiResponse(responseCode = "204", description = "Channel deleted successfully")
    @ApiResponse(responseCode = "400", description = "Validation error")
    @ApiResponse(responseCode = "404", description = "Channel not found")
    @DeleteMapping("${app.business.context-path}/channels/{channelId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void delete(@PathVariable(value = "channelId") UUID channelId) {
        updateChannel.deleteById(channelId, SYSTEM_USER);
    }

    private static ChannelResponse getChannelResponse(Channel channel) {
        ChannelResponse response = new ChannelResponse();
        response.setId(channel.getId());
        response.setName(channel.getName());
        response.setDescription(channel.getDescription());
        response.setChannelType(channel.getChannelType().name());
        response.setStatus(channel.getChannelStatus().name());
        response.setMetadata(channel.getMetadata());
        response.setCreatedAt(Instant.from(channel.getCreatedAt()));
        response.setCreatedBy(channel.getCreatedBy());
        response.setUpdatedAt(Instant.from(channel.getUpdatedAt()));
        response.setUpdatedBy(channel.getUpdatedBy());

        return response;
    }

    @Operation(
            summary = "Search channels by partial name",
            description = "Retrieves a list of channels whose names contain the provided query string. The search is partial and case insensitive. Returns an empty list if no matches are found"
    )
    @ApiResponse(responseCode = "200", description = "Successful retrieval", content = {@Content(mediaType = "application/json", array = @ArraySchema(schema = @Schema(implementation = ChannelResponse.class)))})
    @ApiResponse(responseCode = "400", description = "Validation error")
    @GetMapping("${app.business.context-path}/channels/search")
    public List<ChannelResponse> searchByName(@RequestParam(value = "name") String name) {
        List<Channel> channelList = retrieveChannel.retrieveChannelsByName(name);

        return channelList.stream()
                .map(ChannelController::getChannelResponse)
                .toList();

    }
}
