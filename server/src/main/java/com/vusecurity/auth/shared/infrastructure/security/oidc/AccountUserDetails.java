package com.vusecurity.auth.shared.infrastructure.security.oidc;

import com.vusecurity.auth.contracts.enums.AccountLifecycleState;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity.AccountJpaEntity;
import org.springframework.security.core.CredentialsContainer;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Custom implementation of UserDetails and CredentialsContainer based on
 * AccountJpaEntity.
 * This class provides Spring Security integration for the account-based
 * authentication system.
 */
public class AccountUserDetails implements UserDetails, CredentialsContainer {

    private final AccountJpaEntity account;
    private final Map<String, Object> claimValues;
    private String password;
    private final Collection<GrantedAuthority> authorities;

    public AccountUserDetails(AccountJpaEntity account,
            Map<String, Object> claimValues) {
        this.account = account;
        this.claimValues = claimValues != null ? new HashMap<>(claimValues) : new HashMap<>();
        this.password = ""; // Will be set externally or retrieved from claims
        this.authorities = buildAuthorities();
    }

    public AccountUserDetails(AccountJpaEntity account,
            Map<String, Object> claimValues,
            String password) {
        this(account, claimValues);
        this.password = password;
    }

    private Collection<GrantedAuthority> buildAuthorities() {
        Set<GrantedAuthority> authorities = new HashSet<>();

        // Add role-based authorities
        if (account.getRoles() != null) {
            authorities.addAll(
                    account.getRoles().stream()
                            .map(role -> new SimpleGrantedAuthority("ROLE_" + role.getName().toUpperCase()))
                            .collect(Collectors.toSet()));
        }

        // Add account type as authority
        if (account.getAccountType() != null) {
            authorities.add(new SimpleGrantedAuthority("ACCOUNT_TYPE_" + account.getAccountType().name()));
        }

        return authorities;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return authorities;
    }

    @Override
    public String getPassword() {
        return password;
    }

    @Override
    public String getUsername() {
        /**
         * TBD
         * // Try to get username from claims first
         * String username = getClaimValue("username");
         * if (username != null && !username.trim().isEmpty()) {
         * return username;
         * }
         * 
         * // Fallback to email if available
         * String email = getClaimValue("email_address");
         * if (email != null && !email.trim().isEmpty()) {
         * return email;
         * }
         * 
         * // Fallback to identity name
         * if (account.getIdentity() != null && account.getIdentity().getName() != null)
         * {
         * return account.getIdentity().getName();
         * }
         */
        // Last resort: use account ID
        return account.getId().toString();
    }

    @Override
    public boolean isAccountNonExpired() {
        // Account is not expired if it's not in DELETED state
        return !AccountLifecycleState.DELETED.equals(account.getLifecycleState());
    }

    @Override
    public boolean isAccountNonLocked() {
        // Account is not locked if it's not SUSPENDED
        return !AccountLifecycleState.SUSPENDED.equals(account.getLifecycleState());
    }

    @Override
    public boolean isCredentialsNonExpired() {
        // For now, credentials don't expire
        // This could be enhanced to check password expiration dates from claims or
        // metadata
        return true;
    }

    @Override
    public boolean isEnabled() {
        // Account is enabled if it's ACTIVE
        return AccountLifecycleState.ACTIVE.equals(account.getLifecycleState());
    }

    @Override
    public void eraseCredentials() {
        this.password = null;
    }

    // Additional methods to access account information

    public AccountJpaEntity getAccount() {
        return account;
    }

    public UUID getAccountId() {
        return account.getId();
    }

    public UUID getBusinessId() {
        return account.getBusinessId();
    }

    public UUID getIdentityId() {
        return account.getIdentityId();
    }

    public String getClaimValue(String claimCode) {
        Object value = claimValues.get(claimCode);
        return value != null ? ((Map<String, Object>) value).get("value").toString() : null;
    }

    public Map<String, Object> getAllClaimValues() {
        return new HashMap<>(claimValues);
    }

    public String getDisplayName() {
        // Try to build a display name from claims
        String firstName = getClaimValue("first_name");
        String lastName = getClaimValue("last_name");

        if (firstName != null && lastName != null) {
            return firstName + " " + lastName;
        }

        if (firstName != null) {
            return firstName;
        }

        if (lastName != null) {
            return lastName;
        }

        if (account.getIdentity() != null && account.getIdentity().getName() != null) {
            return account.getIdentity().getName();
        }

        // Fallback to username
        return getUsername();
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null || getClass() != obj.getClass())
            return false;

        AccountUserDetails that = (AccountUserDetails) obj;
        return Objects.equals(account.getId(), that.account.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(account.getId());
    }

    @Override
    public String toString() {
        return "AccountUserDetails{" +
                "accountId=" + account.getId() +
                ", username='" + getUsername() + '\'' +
                ", businessId=" + getBusinessId() +
                ", accountType=" + account.getAccountType() +
                ", lifecycleState=" + account.getLifecycleState() +
                ", authorities=" + authorities.size() +
                '}';
    }
}
