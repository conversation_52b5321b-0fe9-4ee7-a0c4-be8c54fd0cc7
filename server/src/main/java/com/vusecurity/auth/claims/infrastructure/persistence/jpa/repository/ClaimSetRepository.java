package com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository;

import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetJpaEntity;
import com.vusecurity.auth.contracts.enums.AccountType;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.Set;

/**
 * Spring Data JPA repository for ClaimSetJpaEntity.
 * Infrastructure-only interface for database operations.
 */
@Repository
public interface ClaimSetRepository extends JpaRepository<ClaimSetJpaEntity, UUID>, JpaSpecificationExecutor<ClaimSetJpaEntity> {

    /**
     * Find claim set by ID.
     * @param id the claim set ID
     * @return the claim set if found
     */
    Optional<ClaimSetJpaEntity> findById(UUID id);

    /**
     * Find claim set by composite business key (business ID, account type, and identifier flag).
     * @param businessId the business ID
     * @param accountType the account type
     * @param isIdentifier the identifier flag
     * @return the claim set if found
     */
    @Query("SELECT cs FROM ClaimSetJpaEntity cs WHERE cs.business.id = :businessId AND cs.accountType = :accountType AND cs.isIdentifier = :isIdentifier")
    Optional<ClaimSetJpaEntity> findByBusinessIdAndAccountTypeAndIsIdentifier(
            @Param("businessId") UUID businessId,
            @Param("accountType") AccountType accountType,
            @Param("isIdentifier") Boolean isIdentifier
    );

    /**
     * Count claim sets by IDs
     */
    long countByIdIn(Set<UUID> ids);
}