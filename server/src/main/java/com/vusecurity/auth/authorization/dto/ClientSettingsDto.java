package com.vusecurity.auth.authorization.dto;

import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for OAuth2 ClientSettings to be stored as structured data instead of JSON string.
 * Based on Spring OAuth2 ClientSettings properties.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Embeddable
public class ClientSettingsDto {

    /**
     * Whether authorization consent is required when the client requests access.
     * Default: false for confidential clients, true for public clients.
     */
    private Boolean requireAuthorizationConsent;

    /**
     * Whether a proof key is required when the client performs the Authorization Code Grant flow.
     * Default: false for confidential clients, true for public clients.
     */
    private Boolean requireProofKey;

    /**
     * The URL for the client's JSON Web Key Set.
     * Used for client authentication and token validation.
     */
    private String jwkSetUrl;

    /**
     * The signing algorithm used for JWT client assertions.
     * Common values: RS256, ES256, PS256
     */
    private String tokenEndpointAuthenticationSigningAlgorithm;

    /**
     * The subject distinguished name of the client's X.509 certificate.
     * Used for mutual TLS client authentication.
     */
    private String x509CertificateSubjectDN;

    /**
     * Creates a ClientSettingsDto with default values.
     */
    public static ClientSettingsDto withDefaults() {
        ClientSettingsDto dto = new ClientSettingsDto();
        dto.setRequireAuthorizationConsent(false);
        dto.setRequireProofKey(false);
        return dto;
    }
}
