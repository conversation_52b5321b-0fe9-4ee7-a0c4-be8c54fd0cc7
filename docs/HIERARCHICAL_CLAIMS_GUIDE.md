# Guía de Claims Jerárquicos con CLAIMSET

Esta guía explica cómo implementar y usar el sistema de claims jer<PERSON>rquicos usando el tipo de datos `CLAIMSET` para crear estructuras tipo árbol.

## Caso de Uso: Direcciones Múltiples

Supongamos que Ana tiene múltiples direcciones (casa y trabajo) y queremos poder buscar por tipo de dirección y obtener todos los datos relacionados.

### Estructura de Datos

```
ClaimDefinition: "direcciones" (ARRAY, isAListOf="direccion_def")
    ├── [0] → ClaimSet: "direccion_ana_casa"
    │   ├── ClaimDefinition: "tipo" → ClaimValue: "Casa"
    │   ├── ClaimDefinition: "calle" → ClaimValue: "Calle 123"
    │   └── ClaimDefinition: "ciudad" → ClaimValue: "Madrid"
    └── [1] → ClaimSet: "direccion_ana_trabajo"
        ├── ClaimDefinition: "tipo" → ClaimValue: "Trabajo"
        ├── ClaimDefinition: "calle" → ClaimValue: "Av. Principal 456"
        └── ClaimDefinition: "ciudad" → ClaimValue: "Barcelona"
```

### Configuración Paso a Paso

#### 1. Crear ClaimDefinitions Base

```json
// ClaimDefinition para el array principal
{
  "code": "direcciones",
  "name": "Direcciones",
  "dataType": "ARRAY",
  "isAListOf": "direccion_def_id"
}

// ClaimDefinition que define la estructura de cada dirección
{
  "code": "direccion_def",
  "name": "Definición de Dirección",
  "dataType": "CLAIMSET"
}

// ClaimDefinitions para los componentes de dirección
{
  "code": "tipo",
  "name": "Tipo de Dirección",
  "dataType": "STRING"
}

{
  "code": "calle",
  "name": "Calle",
  "dataType": "STRING"
}

{
  "code": "ciudad",
  "name": "Ciudad",
  "dataType": "STRING"
}
```

#### 2. Crear ClaimSets

```json
// ClaimSet para dirección de casa
{
  "name": "direccion_ana_casa",
  "description": "Dirección de casa de Ana",
  "isIdentifier": false,
  "businessId": "business_id",
  "accountType": "EMPLOYEE"
}

// ClaimSet para dirección de trabajo
{
  "name": "direccion_ana_trabajo", 
  "description": "Dirección de trabajo de Ana",
  "isIdentifier": false,
  "businessId": "business_id",
  "accountType": "EMPLOYEE"
}
```

#### 3. Asociar ClaimDefinitions a ClaimSets

Agregar las ClaimDefinitions `tipo`, `calle`, y `ciudad` a ambos ClaimSets.

#### 4. Crear ClaimValues

```json
// Para ClaimSet "direccion_ana_casa"
{
  "claimDefinitionId": "tipo_id",
  "accountId": "ana_account_id",
  "value": "Casa",
  "claimSetId": "direccion_ana_casa_id"
}

{
  "claimDefinitionId": "calle_id", 
  "accountId": "ana_account_id",
  "value": "Calle 123",
  "claimSetId": "direccion_ana_casa_id"
}

{
  "claimDefinitionId": "ciudad_id",
  "accountId": "ana_account_id", 
  "value": "Madrid",
  "claimSetId": "direccion_ana_casa_id"
}

// Para ClaimSet "direccion_ana_trabajo"
{
  "claimDefinitionId": "tipo_id",
  "accountId": "ana_account_id",
  "value": "Trabajo", 
  "claimSetId": "direccion_ana_trabajo_id"
}

{
  "claimDefinitionId": "calle_id",
  "accountId": "ana_account_id",
  "value": "Av. Principal 456",
  "claimSetId": "direccion_ana_trabajo_id"
}

{
  "claimDefinitionId": "ciudad_id",
  "accountId": "ana_account_id",
  "value": "Barcelona", 
  "claimSetId": "direccion_ana_trabajo_id"
}
```

#### 5. Crear el ClaimValue del Array Principal

```json
{
  "claimDefinitionId": "direcciones_id",
  "accountId": "ana_account_id",
  "value": ["direccion_ana_casa_id", "direccion_ana_trabajo_id"]
}
```

### Consultas Jerárquicas

#### Buscar Valores Relacionados

Para obtener todos los datos de dirección cuando sabemos que Ana tiene una dirección de tipo "Trabajo":

```http
GET /api/v1/claim-values/related?ownerType=ACCOUNT&ownerId=ana_account_id&searchValue=Trabajo
```

**Respuesta:**
```json
[
  {
    "id": "uuid1",
    "claimDefinitionId": "tipo_id",
    "value": "Trabajo",
    "ownerType": "ACCOUNT",
    "ownerId": "ana_account_id"
  },
  {
    "id": "uuid2", 
    "claimDefinitionId": "calle_id",
    "value": "Av. Principal 456",
    "ownerType": "ACCOUNT",
    "ownerId": "ana_account_id"
  },
  {
    "id": "uuid3",
    "claimDefinitionId": "ciudad_id", 
    "value": "Barcelona",
    "ownerType": "ACCOUNT",
    "ownerId": "ana_account_id"
  }
]
```

### Ventajas del Sistema

1. **Búsqueda Contextual**: Al buscar "Trabajo" obtienes automáticamente la calle y ciudad asociadas
2. **Estructura Flexible**: Puedes agregar más campos a las direcciones sin cambiar la estructura base
3. **Reutilización**: Los ClaimDefinitions se pueden reutilizar en diferentes ClaimSets
4. **Escalabilidad**: Fácil agregar más direcciones o más tipos de datos jerárquicos

### Casos de Uso Adicionales

- **Contactos**: Múltiples números de teléfono con tipo (personal, trabajo, emergencia)
- **Educación**: Múltiples títulos con institución, fecha, nivel
- **Experiencia Laboral**: Múltiples trabajos con empresa, puesto, fechas
- **Documentos**: Múltiples documentos con tipo, número, fecha de vencimiento

## Tipo de Datos CLAIMSET

El tipo `CLAIMSET` valida que el valor sea un UUID válido que corresponda a un ClaimSet existente en la base de datos.

### Validación

```java
// Válido - UUID de ClaimSet existente
"123e4567-e89b-12d3-a456-************"

// Inválido - No es UUID
"not-a-uuid"

// Inválido - UUID malformado  
"123e4567-invalid-uuid"
```

### Normalización

El tipo `CLAIMSET` normaliza los UUIDs eliminando espacios en blanco y asegurando el formato estándar.
