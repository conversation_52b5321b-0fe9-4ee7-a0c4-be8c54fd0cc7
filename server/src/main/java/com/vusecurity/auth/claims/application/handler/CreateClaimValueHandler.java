package com.vusecurity.auth.claims.application.handler;

import com.vusecurity.auth.claims.application.command.CreateClaimValueCommand;
import com.vusecurity.auth.claims.application.exception.ClaimDefinitionNotFoundException;
import com.vusecurity.auth.claims.application.exception.DuplicateClaimValueException;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimSetDefinitionMappingJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimValueJpaEntity;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimDefinitionRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimSetDefinitionMappingRepository;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.repository.ClaimValueRepository;
import com.vusecurity.auth.contracts.enums.DataTypeEnum;
import com.vusecurity.auth.contracts.enums.OwnerType;
import com.vusecurity.auth.identities.application.dto.AccountBusinessInfo;
import com.vusecurity.auth.identities.application.exception.AccountNotFoundException;
import com.vusecurity.auth.identities.infrastructure.persistence.jpa.repository.AccountRepository;
import com.vusecurity.auth.shared.util.DatabaseConstraintUtils;
import com.vusecurity.auth.shared.util.DataFormatUtils;
import com.vusecurity.auth.shared.util.ArrayParsingUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class CreateClaimValueHandler {

    private static final String UX_OWNER_CD_VAL = "ux_claim_value_owner_cd_val";

    private final ClaimValueRepository valueRepo;
    private final ClaimDefinitionRepository defRepo;
    private final ClaimSetDefinitionMappingRepository mappingRepo;
    private final AccountRepository accountRepo;

    @Transactional
    public ClaimValueJpaEntity handle(CreateClaimValueCommand cmd) {

        cmd.validate();

        ClaimDefinitionJpaEntity definition = defRepo.findById(cmd.claimDefinitionId())
                .orElseThrow(() -> new ClaimDefinitionNotFoundException(cmd.claimDefinitionId()));

        /* ---------------- VALUE MATCH WITH PATTERN ------------------------ */

        validateWithDataFormat(cmd, definition);

        /* ---------------- VALUE MATCH WITH TYPE ------------------------ */
        DataTypeEnum type = definition.getDataType();

        if (!type.isValid(cmd.value())) {
            throw new IllegalArgumentException("Value does not match data type: " + type.name());
        }

        /* ---------------- VALUE NORMALIZED ------------------------ */
        String normalizedValue = type.normalize(cmd.value());

        /* ---------------- IDENTITY PATH ----------------------------------- */
        if (cmd.identityId() != null) {
            return save(definition, OwnerType.IDENTITY, cmd.identityId(), cmd, normalizedValue);
        }

        /* ---------------- ACCOUNT PATH ------------------------------------- */
        UUID accountId = cmd.accountId();

        AccountBusinessInfo info = accountRepo.findInfoById(accountId)
                .orElseThrow(() -> new AccountNotFoundException(accountId));

        var mappings = mappingRepo.findApplicableMappings(
                        info.getBusinessId(),
                        info.getAccountType(),
                        definition.getId());
        
        if (mappings.isEmpty()) {
            throw new IllegalStateException(
                    "No applicable mapping for claim definition " + definition.getId());
        }

        // Business Rule: Uniqueness is enforced if ANY mapping requires it
        boolean uniquenessRequired = mappings.stream().anyMatch(mapping ->
                Boolean.TRUE.equals(mapping.getClaimSet().getIsIdentifier()) ||
                        Boolean.TRUE.equals(mapping.getEnforceUniqueness()));

        /* -------- Rule: at most one value for this owner & CD ----------- */
        if (uniquenessRequired &&
                valueRepo.existsByOwnerTypeAndOwnerIdAndClaimDefinitionId(
                        OwnerType.ACCOUNT, accountId, definition.getId())) {
            throw new DuplicateClaimValueException(
                    "Account already has a value for claim " + definition.getCode());
        }

        /* -------- Rule: no duplicate across *other* accounts of same AT - */
        if (uniquenessRequired &&
                valueRepo.existsForAccountType(
                        info.getAccountType(),
                        definition.getId(),
                        cmd.value().toLowerCase(),   // normalised for case-insensitivity
                        accountId                    // exclude the caller itself
                )) {
            throw new DuplicateClaimValueException(
                    "Value '" + cmd.value() + "' is already used by another "
                            + info.getAccountType() + " account");
        }

        return save(definition, OwnerType.ACCOUNT, accountId, cmd, normalizedValue);
    }


    private ClaimValueJpaEntity save(ClaimDefinitionJpaEntity def,
                                     OwnerType ownerType,
                                     UUID ownerId,
                                     CreateClaimValueCommand cmd,
                                     String valueNormalized) {

        ClaimValueJpaEntity cv = new ClaimValueJpaEntity(def, ownerType, ownerId);
        cv.setValue(valueNormalized);
        cv.setPrimary(cmd.isPrimary());
        cv.setComputed(cmd.isComputed());
        cv.setSource(cmd.source());

        if (ownerType.equals(OwnerType.ACCOUNT)){
            Optional<ClaimSetDefinitionMappingJpaEntity> csdMapping = def.getClaimSetMappings().stream().filter(
                csm ->
                    csm.getClaimSet().getId().equals(cmd.claimSetId()) &&
                    csm.getClaimDefinition().getId().equals(cmd.claimDefinitionId())
                ).findFirst();
            if (csdMapping.isPresent()){
                ClaimSetClaimValueJpaEntity relation = new ClaimSetClaimValueJpaEntity();
                relation.setClaimSet(csdMapping.get().getClaimSet());
                relation.setClaimValue(cv);
                cv.setClaimSetClaimValue(relation);
            } else {
                throw new IllegalStateException("No applicable path for claimSet " + cmd.claimSetId() + " and claimDefinition " + cmd.claimDefinitionId());
            }
        }

        // Use utility to handle constraint violations
        return DatabaseConstraintUtils.executeWithConstraintHandling(
                () -> valueRepo.saveAndFlush(cv), // we are forcefully flushing to catch constraint violations immediately
                UX_OWNER_CD_VAL,
                () -> {
                    String owner = ownerType == OwnerType.ACCOUNT ? "account" : "identity";
                    return new DuplicateClaimValueException(
                            "The %s already has the value '%s' for claim '%s'"
                                    .formatted(owner, cmd.value(), def.getCode()));
                }
        );
    }

    private void validateWithDataFormat(CreateClaimValueCommand cmd, ClaimDefinitionJpaEntity definition) {
        String dataFormat = definition.getDataFormat();
        if (dataFormat == null || dataFormat.isBlank()) {
            return; // No validation pattern specified
        }

        // For ARRAY types, check if dataFormat is a UUID reference to another ClaimDefinition
        if (definition.getDataType() == DataTypeEnum.ARRAY && DataFormatUtils.isUuidFormat(dataFormat)) {
            validateArrayWithUuidReference(cmd, dataFormat);
        } else {
            // For non-ARRAY types or when dataFormat is not UUID, use regex validation
            cmd.validateWithPattern(dataFormat);
        }
    }

    private void validateArrayWithUuidReference(CreateClaimValueCommand cmd, String referencedClaimDefinitionId) {
        try {
            UUID referencedId = UUID.fromString(referencedClaimDefinitionId.trim());
            ClaimDefinitionJpaEntity referencedDefinition = defRepo.findById(referencedId)
                    .orElseThrow(() -> new IllegalArgumentException(
                            "Referenced claim definition not found: " + referencedClaimDefinitionId));

            // Parse the array value and validate each element against the referenced definition's pattern
            String[] arrayElements = ArrayParsingUtils.parseArrayValue(cmd.value());
            String referencedPattern = referencedDefinition.getDataFormat();

            for (String element : arrayElements) {
                if (referencedPattern != null && !referencedPattern.isBlank() && !element.trim().matches(referencedPattern)) {
                    throw new IllegalArgumentException(
                            "Array element '" + element + "' does not match the required pattern for " +
                            referencedDefinition.getCode());
                }

                // Also validate against the referenced definition's data type
                DataTypeEnum referencedType = referencedDefinition.getDataType();
                if (!referencedType.isValid(element.trim())) {
                    throw new IllegalArgumentException(
                            "Array element '" + element + "' does not match data type: " + referencedType.name());
                }
            }
        } catch (IllegalArgumentException e) {
            throw e;
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid UUID reference in dataFormat: " + referencedClaimDefinitionId, e);
        }
    }


}
