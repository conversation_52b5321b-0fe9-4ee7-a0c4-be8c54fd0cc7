package com.vusecurity.auth.shared.test;

import com.vusecurity.multitenant.test.TestTenantContextManager;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;

/**
 * Base class for integration tests that provides:
 * - Shared TestContainers SQL Server setup
 * - Tenant context management
 * - Common test configuration
 * - Single datasource mode for simplified testing
 * - Automatic database cleanup between tests
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
@ExtendWith(TestTenantContextManager.class)
public abstract class BaseIntegrationTest {

    @Autowired
    private TestDatabaseCleaner databaseCleaner;

    @BeforeAll
    static void setupContainerLogging() {
        // Enable container logging for debugging
        SharedTestContainers.enableContainerLogging();
    }

    @BeforeEach
    void cleanupDatabase() {
        // Clean database before each test for isolation
        databaseCleaner.smartCleanDatabase();
    }

    @DynamicPropertySource
    static void configureTestProperties(DynamicPropertyRegistry registry) {
        // Use shared container configuration
        SharedTestContainers.configureProperties(registry);
    }

    /**
     * Helper method to execute code within a specific tenant context
     */
    protected void withTenantContext(String tenantId, Runnable action) {
        TestTenantContextManager.withTenantContext(tenantId, action);
    }

    /**
     * Helper method to get current tenant
     */
    protected String getCurrentTenant() {
        return TestTenantContextManager.getCurrentTenant();
    }

    /**
     * Helper method to set tenant context
     */
    protected void setTenantContext(String tenantId) {
        TestTenantContextManager.setTenantContext(tenantId);
    }
}
