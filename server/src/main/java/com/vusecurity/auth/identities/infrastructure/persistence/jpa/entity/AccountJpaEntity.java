package com.vusecurity.auth.identities.infrastructure.persistence.jpa.entity;

import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.GroupMembershipJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.RoleJpaEntity;
import com.vusecurity.auth.contracts.enums.AccountLifecycleState;
import com.vusecurity.auth.contracts.enums.AccountType;
import com.vusecurity.auth.shared.infrastructure.persistence.jpa.AbstractEntity;
import com.vusecurity.business.domain.Business;
import com.vusecurity.core.commons.utils.converters.MapToJsonConverter;

import jakarta.persistence.*;

import java.time.Instant;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;

@Entity
@Table(name = "account",
        indexes = {
                @Index(name = "uk_account_id", columnList = "id", unique = true),
                @Index(name = "idx_account_business_id", columnList = "business_id"),
                @Index(name = "idx_account_identity_id", columnList = "identity_id")
        },
        uniqueConstraints = {
                @UniqueConstraint(
                        name = "uk_account_type_business_identity_provider_identity",
                        columnNames = {"accountType", "business_id", "identity_provider_id", "identity_id"}
                )
        })
@SuppressWarnings("LombokGetterMayBeUsed, LombokSetterMayBeUsed")
public class AccountJpaEntity extends AbstractEntity {

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(
            name = "business_id",           // column in account table
            foreignKey = @ForeignKey(name = "fk_account_business"),
            referencedColumnName = "id"     // PK of Business
    )
    private Business business;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "identity_id", referencedColumnName = "id")
    private IdentityJpaEntity identity;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private AccountType accountType;

    @Convert(converter = MapToJsonConverter.class)
    private Map<String, Object> metadata = new HashMap<>();

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private AccountLifecycleState lifecycleState = AccountLifecycleState.NONE;

    private UUID mergePrimary;

    private UUID mergeSecondary;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(
            name = "identity_provider_id",  // column in account table
            foreignKey = @ForeignKey(name = "fk_account_identity_provider"),
            referencedColumnName = "id"     // PK of IdentityProviderJpaEntity
    )
    private IdentityProviderJpaEntity identityProvider;

    @ManyToMany(mappedBy = "accounts", fetch = FetchType.LAZY)    
    private Set<RoleJpaEntity> roles = new HashSet<>();

    @OneToMany(mappedBy = "account", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    private Set<GroupMembershipJpaEntity> groupMemberships = new HashSet<>();

    // Constructors
    public AccountJpaEntity() {
    }

    public AccountJpaEntity(Business business,
                            IdentityJpaEntity identity,
                            IdentityProviderJpaEntity identityProvider,
                            AccountType accountType) {

        if (business == null || identity == null || identityProvider == null || accountType == null) {
            throw new IllegalArgumentException("business, identity, identityProvider and accountType are mandatory");
        }
        this.business = business;
        this.identity = identity;
        this.identityProvider = identityProvider;
        this.accountType = accountType;
        this.lifecycleState = AccountLifecycleState.NONE;
        this.metadata = new HashMap<>();
        this.roles = new HashSet<>();
    }

    public AccountJpaEntity(UUID id,
                            Business business,
                            IdentityJpaEntity identity,
                            IdentityProviderJpaEntity identityProvider,
                            AccountType accountType) {
        this(business, identity, identityProvider, accountType);
        this.setId(id);
    }

    public UUID getIdentityId() {
        return identity != null ? identity.getId() : null;
    }

    public UUID getBusinessId() {
        return business != null ? business.getId() : null;
    }

    public UUID getIdentityProviderId() {
        return identityProvider != null ? identityProvider.getId() : null;
    }

    // Getters
    public Business getBusiness() {
        return business;
    }

    public IdentityJpaEntity getIdentity() {
        return identity;
    }

    public AccountType getAccountType() {
        return accountType;
    }

    public AccountLifecycleState getLifecycleState() {
        return lifecycleState;
    }

    public UUID getMergePrimary() {
        return mergePrimary;
    }

    public UUID getMergeSecondary() {
        return mergeSecondary;
    }

    public IdentityProviderJpaEntity getIdentityProvider() {
        return identityProvider;
    }

    public Set<RoleJpaEntity> getRoles() {
        return new HashSet<>(roles); // Return defensive copy
    }

    public Set<GroupMembershipJpaEntity> getGroupMemberships() {
        return new HashSet<>(groupMemberships);
    }

    // Setters
    public void setBusiness(Business business) {
        this.business = business;
    }

    public void setIdentity(IdentityJpaEntity identity) {
        this.identity = identity;
    }

    public void setAccountType(AccountType accountType) {
        this.accountType = accountType;
    }

    public void setLifecycleState(AccountLifecycleState lifecycleState) {
        this.lifecycleState = lifecycleState;
    }

    public void setMergePrimary(UUID mergePrimary) {
        this.mergePrimary = mergePrimary;
    }

    public void setMergeSecondary(UUID mergeSecondary) {
        this.mergeSecondary = mergeSecondary;
    }

    public void setIdentityProvider(IdentityProviderJpaEntity identityProvider) {
        this.identityProvider = identityProvider;
    }

    // Business behavior methods
    public void activate() {
        this.lifecycleState = AccountLifecycleState.ACTIVE;
        this.setUpdatedAt(Instant.now());
    }

    public void suspend() {
        if (!isActive()) {
            throw new IllegalStateException("Cannot suspend non-active account");
        }
        this.lifecycleState = AccountLifecycleState.SUSPENDED;
        this.setUpdatedAt(Instant.now());
    }

    public void deactivate() {
        this.lifecycleState = AccountLifecycleState.DEACTIVATED;
        this.setUpdatedAt(Instant.now());
    }

    public boolean isActive() {
        return AccountLifecycleState.ACTIVE.equals(this.lifecycleState);
    }

    public boolean isSuspended() {
        return AccountLifecycleState.SUSPENDED.equals(this.lifecycleState);
    }

    public boolean isInactive() {
        return AccountLifecycleState.DEACTIVATED.equals(this.lifecycleState);
    }

    public void addMetadata(String key, Object value) {
        if (key == null || key.trim().isEmpty()) {
            throw new IllegalArgumentException("Metadata key cannot be null or empty");
        }
        this.metadata.put(key, value);
        this.setUpdatedAt(Instant.now());
    }

    public void removeMetadata(String key) {
        this.metadata.remove(key);
        this.setUpdatedAt(Instant.now());
    }

    public void assignRole(String roleName) {
        if (roleName == null || roleName.trim().isEmpty()) {
            throw new IllegalArgumentException("Role name cannot be null or empty");
        }
        // Note: This is a simplified version. In a real implementation,
        // you would need to load the RoleJpaEntity and add it to the roles set
        this.setUpdatedAt(Instant.now());
    }

    public void removeRole(String roleName) {
        this.roles.removeIf(role -> roleName.equals(role.getName()));
        this.setUpdatedAt(Instant.now());
    }

    public boolean hasRole(String roleName) {
        return roles.stream()
                .anyMatch(role -> roleName.equals(role.getName()));
    }

    public Set<String> getRoleNames() {
        return roles.stream()
                .map(RoleJpaEntity::getName)
                .collect(java.util.stream.Collectors.toSet());
    }

    // Custom getters for defensive copies
    public Map<String, Object> getMetadata() {
        return new HashMap<>(metadata); // Return defensive copy
    }

    // Custom setters for defensive copies
    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata != null ? new HashMap<>(metadata) : new HashMap<>();
        this.setUpdatedAt(Instant.now());
    }

    public void setRoles(Set<RoleJpaEntity> roles) {
        this.roles = roles != null ? new HashSet<>(roles) : new HashSet<>();
        this.setUpdatedAt(Instant.now());
    }

    public void setGroupMemberships(Set<GroupMembershipJpaEntity> groupMemberships) {
        this.groupMemberships = groupMemberships != null ? new HashSet<>(groupMemberships) : new HashSet<>();
        this.setUpdatedAt(Instant.now());
    }

    // Helpers to maintain bidirectional relation with GroupMembershipJpaEntity
    public void addGroupMembership(GroupMembershipJpaEntity membership) {
        if (membership == null) {
            throw new IllegalArgumentException("membership cannot be null");
        }
        membership.setAccount(this);
        this.groupMemberships.add(membership);
        this.setUpdatedAt(Instant.now());
    }

    public void removeGroupMembership(GroupMembershipJpaEntity membership) {
        if (membership == null) return;
        this.groupMemberships.remove(membership);
        try {
            membership.setAccount(null);
        } catch (Exception ignored) {
        }
        this.setUpdatedAt(Instant.now());
    }

    // equals() and hashCode() using id (fallback since no unique business key)
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        AccountJpaEntity that = (AccountJpaEntity) o;
        return Objects.equals(getId(), that.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getId());
    }

    @Override
    public String toString() {
        return "AccountJpaEntity{" +
                "id=" + getId() +
                ", accountType=" + accountType +
                ", lifecycleState=" + lifecycleState +
                ", mergePrimary=" + mergePrimary +
                ", mergeSecondary=" + mergeSecondary +
                '}';
    }
}
