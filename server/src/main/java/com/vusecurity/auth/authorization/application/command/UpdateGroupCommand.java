package com.vusecurity.auth.authorization.application.command;

import com.vusecurity.auth.authorization.application.exception.InvalidGroupRequestException;

import java.util.*;

public record UpdateGroupCommand(
        UUID groupId,
        String name,
        String description,
        List<UUID> roleIds,
        List<UUID> accountIds,
        List<UUID> ownerAccountIds,
        Map<String, Object> metadata
) {
    public UpdateGroupCommand {
        if (roleIds == null) {
            roleIds = Collections.emptyList();
        }
        if (accountIds == null) {
            accountIds = Collections.emptyList();
        }
        if (ownerAccountIds == null) {
            ownerAccountIds = Collections.emptyList();
        }
    }

    public void validate() {
        if (groupId == null) {
            throw new InvalidGroupRequestException("groupId is required");
        }
        if (name == null || name.trim().isEmpty()) {
            throw new InvalidGroupRequestException("name is required");
        }
        if (roleIds.isEmpty() && accountIds.isEmpty()) {
            throw new InvalidGroupRequestException("Either roleIds or accountIds must be provided");
        }
        if (!new HashSet<>(accountIds).containsAll(ownerAccountIds)) {
            throw new InvalidGroupRequestException("All ownerAccountIds must also be present in accountIds");
        }
    }
} 