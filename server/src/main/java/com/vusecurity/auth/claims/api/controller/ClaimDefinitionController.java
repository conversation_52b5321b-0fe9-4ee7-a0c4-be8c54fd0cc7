package com.vusecurity.auth.claims.api.controller;

import com.vusecurity.auth.claims.application.command.*;
import com.vusecurity.auth.claims.application.handler.*;
import com.vusecurity.auth.claims.application.query.GetClaimDefinitionQuery;
import com.vusecurity.auth.claims.application.query.GetClaimDefinitionsPagedQuery;
import com.vusecurity.auth.claims.infrastructure.persistence.jpa.entity.ClaimDefinitionJpaEntity;
import com.vusecurity.auth.claims.mapper.ClaimDefinitionDtoMapper;
import com.vusecurity.auth.contracts.api.v1.dto.claims.claimdefinition.*;
import com.vusecurity.auth.shared.util.UrlParameterUtils;
import com.vusecurity.core.commons.models.PageableResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@SecurityRequirement(name = "oauth2 client credentials")
@Tag(name = "Claim Definitions", description = "Claim definition management operations")
public class ClaimDefinitionController {

    private final CreateClaimDefinitionHandler createClaimDefinitionHandler;
    private final UpdateClaimDefinitionHandler updateClaimDefinitionHandler;
    private final DeleteClaimDefinitionHandler deleteClaimDefinitionHandler;
    private final AddClaimSetsToClaimDefinitionHandler addClaimSetsToClaimDefinitionHandler;
    private final RemoveClaimSetsFromClaimDefinitionHandler removeClaimSetsFromClaimDefinitionHandler;
    private final ReplaceClaimSetsInClaimDefinitionHandler replaceClaimSetsInClaimDefinitionHandler;
    private final PatchClaimSetsOnClaimDefinitionHandler patchClaimSetsOnClaimDefinitionHandler;
    private final GetClaimDefinitionQuery getClaimDefinitionQuery;
    private final ClaimDefinitionDtoMapper claimDefinitionDtoMapper;

    public ClaimDefinitionController(CreateClaimDefinitionHandler createClaimDefinitionHandler, UpdateClaimDefinitionHandler updateClaimDefinitionHandler, DeleteClaimDefinitionHandler deleteClaimDefinitionHandler, AddClaimSetsToClaimDefinitionHandler addClaimSetsToClaimDefinitionHandler, RemoveClaimSetsFromClaimDefinitionHandler removeClaimSetsFromClaimDefinitionHandler, ReplaceClaimSetsInClaimDefinitionHandler replaceClaimSetsInClaimDefinitionHandler, PatchClaimSetsOnClaimDefinitionHandler patchClaimSetsOnClaimDefinitionHandler, GetClaimDefinitionQuery getClaimDefinitionQuery, ClaimDefinitionDtoMapper claimDefinitionDtoMapper) {
        this.createClaimDefinitionHandler = createClaimDefinitionHandler;
        this.updateClaimDefinitionHandler = updateClaimDefinitionHandler;
        this.deleteClaimDefinitionHandler = deleteClaimDefinitionHandler;
        this.addClaimSetsToClaimDefinitionHandler = addClaimSetsToClaimDefinitionHandler;
        this.removeClaimSetsFromClaimDefinitionHandler = removeClaimSetsFromClaimDefinitionHandler;
        this.replaceClaimSetsInClaimDefinitionHandler = replaceClaimSetsInClaimDefinitionHandler;
        this.patchClaimSetsOnClaimDefinitionHandler = patchClaimSetsOnClaimDefinitionHandler;
        this.getClaimDefinitionQuery = getClaimDefinitionQuery;
        this.claimDefinitionDtoMapper = claimDefinitionDtoMapper;
    }

    @Operation(
            summary = "Get all claim definitions",
            description = "Retrieves a paginated list of claim definitions with advanced filtering options. Supports filtering by code, name, description, data type, and list type. Use isAList parameter to filter for array/list type claims."
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Successful retrieval",
                    content = @Content(schema = @Schema(implementation = PageableResponse.class))),
            @ApiResponse(responseCode = "400", description = "Invalid request parameters"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("${app.claims.context-path}/claim-definitions")
    public PageableResponse<ClaimDefinitionResponse> get(
            @RequestParam(name = "page", defaultValue = "1") int page,
            @RequestParam(name = "pageSize", defaultValue = "10") int pageSize,
            @RequestParam(name = "claimSetId", required = false) UUID claimDefinitionId,
            @RequestParam(name = "code", required = false) String code,
            @RequestParam(name = "name", required = false) String name,
            @RequestParam(name = "description", required = false) String description,
            @RequestParam(name = "dataType", required = false) String dataType,
            @RequestParam(name = "isAList", required = false) Boolean isAList,
            @RequestParam(name = "filter", required = false) String filter) {

        // Decode URL-encoded parameters to handle special characters properly
        String decodedCode = UrlParameterUtils.decodeAndSanitizeParameter(code);
        String decodedName = UrlParameterUtils.decodeAndSanitizeParameter(name);
        String decodedDescription = UrlParameterUtils.decodeAndSanitizeParameter(description);
        String decodedDataType = UrlParameterUtils.decodeAndSanitizeParameter(dataType);
        String decodedFilter = UrlParameterUtils.decodeAndSanitizeParameter(filter);

        GetClaimDefinitionsPagedQuery query = GetClaimDefinitionsPagedQuery.builder()
                .page(page)
                .pageSize(pageSize)
                .claimDefinitionId(claimDefinitionId)
                .code(decodedCode)
                .name(decodedName)
                .description(decodedDescription)
                .dataType(decodedDataType)
                .isAList(isAList)
                .filter(decodedFilter)
                .build();

        Page<ClaimDefinitionJpaEntity> claimDefinitions = getClaimDefinitionQuery.getAllClaimDefinitions(query);
        return new PageableResponse<>(claimDefinitions.getNumber() + 1, claimDefinitions.getSize(),
                claimDefinitions.getTotalElements(),
                claimDefinitions.get().map(claimDefinitionDtoMapper::toResponse).toList());
    }

    @Operation(
            summary = "Create a new claim definition",
            description = "Creates a new claim definition with the specified code, name, description, data type, and optional format validation. The code and name must be unique."
    )
    @ApiResponses({
            @ApiResponse(responseCode = "201", description = "Claim definition created successfully",
                    content = @Content(schema = @Schema(implementation = ClaimDefinitionResponse.class))),
            @ApiResponse(responseCode = "400", description = "Invalid request data or validation errors"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Access denied"),
            @ApiResponse(responseCode = "409", description = "Claim definition with same code already exists")
    })
    @PostMapping("${app.claims.context-path}/claim-definitions")
    @ResponseStatus(HttpStatus.CREATED)
    public ClaimDefinitionResponse create(@RequestBody @Valid CreateClaimDefinitionRequest request) {

        CreateClaimDefinitionCommand command = new CreateClaimDefinitionCommand(
                request.getCode(),
                request.getName(),
                request.getDescription(),
                request.getClaimType(),
                request.getDataType(),
                request.getDataFormat()
        );

        ClaimDefinitionJpaEntity claimDefinition = createClaimDefinitionHandler.createClaimDefinition(command);
        return claimDefinitionDtoMapper.toResponse(claimDefinition);
    }


    @Operation(
            summary = "Get claim definition by ID",
            description = "Retrieves a specific claim definition by its unique identifier."
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Claim definition found",
                    content = @Content(schema = @Schema(implementation = ClaimDefinitionResponse.class))),
            @ApiResponse(responseCode = "404", description = "Claim definition not found"),
            @ApiResponse(responseCode = "401", description = "Authentication required"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("${app.claims.context-path}/claim-definitions/{id}")
    public ClaimDefinitionResponse get(@PathVariable(value = "id") UUID id) {

        ClaimDefinitionJpaEntity claimDefinition = getClaimDefinitionQuery.getClaimDefinitionById(id);
        return claimDefinitionDtoMapper.toResponse(claimDefinition);
    }

    @Operation(summary = "Delete a claim definition", description = "Deletes a claim definition by its ID.")
    @ApiResponses({
            @ApiResponse(responseCode = "204", description = "ClaimDefinition deleted successfully"),
            @ApiResponse(responseCode = "404", description = "ClaimDefinition not found")
    })
    @DeleteMapping(path = "${app.claims.context-path}/claim-definitions/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void delete(@PathVariable(value = "id") UUID id) {

        DeleteClaimDefinitionCommand command = new DeleteClaimDefinitionCommand(id);
        deleteClaimDefinitionHandler.deleteClaimDefinition(command);
    }

    @Operation(summary = "Update a claim definition", description = "Updates a claim definition's properties.")
    @ApiResponses({
            @ApiResponse(responseCode = "204", description = "ClaimDefinition updated successfully"),
            @ApiResponse(responseCode = "404", description = "ClaimDefinition not found")
    })
    @PatchMapping(path = "${app.claims.context-path}/claim-definitions/{id}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void update(@PathVariable(value = "id") UUID id, @RequestBody @Valid UpdateClaimDefinitionRequest request) {

        UpdateClaimDefinitionCommand command = new UpdateClaimDefinitionCommand(
                id,
                request.getCode(),
                request.getName(),
                request.getDescription(),
                request.getClaimType(),
                request.getDataType(),
                request.getDataFormat()
        );

        updateClaimDefinitionHandler.updateClaimDefinition(command);
    }


    @Operation(summary = "Assign claim sets to a claim definition", description = "Adds the specified claim sets to a claim definition.")
    @ApiResponses({
            @ApiResponse(responseCode = "201", description = "Claim sets assigned successfully", content = @Content(schema = @Schema(implementation = ClaimDefinitionClaimSetsResponse.class))),
            @ApiResponse(responseCode = "404", description = "Claim definition not found")
    })
    @PostMapping("${app.claims.context-path}/claim-definitions/{claimDefinitionId}/claim-sets")
    @ResponseStatus(HttpStatus.CREATED)
    public ClaimDefinitionClaimSetsResponse assignClaimSets(
            @PathVariable UUID claimDefinitionId,
            @RequestBody @Valid AddClaimSetsToClaimDefinitionRequest request) {

        List<ClaimSetAssignmentCommand> assignments = request.getClaimSets().stream()
                .map(dto -> new ClaimSetAssignmentCommand(dto.getClaimSetId(), dto.getEnforceUniqueness()))
                .toList();

        AddClaimSetsToClaimDefinitionCommand command = new AddClaimSetsToClaimDefinitionCommand(
                claimDefinitionId,
                assignments
        );

        addClaimSetsToClaimDefinitionHandler.addClaimSetsToClaimDefinition(command);

        return new ClaimDefinitionClaimSetsResponse()
                .setClaimDefinitionId(claimDefinitionId)
                .setClaimSets(request.getClaimSets());
    }

    @Operation(summary = "Remove claim sets from a claim definition" , description = "Removes the specified claim sets from a claim definition.")
    @ApiResponses({
            @ApiResponse(responseCode = "204", description = "Claim sets removed successfully"),
            @ApiResponse(responseCode = "404", description = "Claim definition not found")
    })
    @DeleteMapping("${app.claims.context-path}/claim-definitions/{claimDefinitionId}/claim-sets")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void removeClaimSets(
            @PathVariable UUID claimDefinitionId,
            @RequestBody @Valid RemoveClaimSetsFromClaimDefinitionRequest request) {

        RemoveClaimSetsFromClaimDefinitionCommand command = new RemoveClaimSetsFromClaimDefinitionCommand(
                claimDefinitionId,
                request.getClaimSetIds()
        );

        removeClaimSetsFromClaimDefinitionHandler.removeClaimSetsFromClaimDefinition(command);
    }


    @Operation(
            summary = "Replace claim sets in a claim definitions",
            description = "Replaces all existing claim sets in the claim definition with the provided ones. This operation removes all current claim set and adds the new ones."
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Claim set replaced successfully", content = @Content(schema = @Schema(implementation = ClaimDefinitionClaimSetsResponse.class))),
            @ApiResponse(responseCode = "404", description = "Claim definitions not found"),
            @ApiResponse(responseCode = "400", description = "Invalid request data or one or more claim set not found")
    })
    @PutMapping("${app.claims.context-path}/claim-definitions/{claimDefinitionId}/claim-sets")
    @ResponseStatus(HttpStatus.OK)
    public ClaimDefinitionClaimSetsResponse replaceClaimSets(
            @PathVariable UUID claimDefinitionId,
            @RequestBody @Valid ReplaceClaimSetsOnClaimDefinitionRequest request) {

        List<ClaimSetAssignmentCommand> assignments = request.getClaimSets().stream()
                .map(dto -> new ClaimSetAssignmentCommand(dto.getClaimSetId(), dto.getEnforceUniqueness()))
                .toList();

        ReplaceClaimSetsInClaimDefinitionCommand command = new ReplaceClaimSetsInClaimDefinitionCommand(
                claimDefinitionId,
                assignments
        );

        replaceClaimSetsInClaimDefinitionHandler.replaceClaimSetsInClaimDefinition(command);

        return new ClaimDefinitionClaimSetsResponse()
                .setClaimDefinitionId(claimDefinitionId)
                .setClaimSets(request.getClaimSets());
    }

    @Operation(
            summary = "Partially update claim sets in a claim definition",
            description = "Partially updates claim sets associations in a claim definition. Allows adding new claim sets and updating properties of existing ones without affecting unmentioned associations."
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Claim sets patched successfully", content = @Content(schema = @Schema(implementation = ClaimDefinitionClaimSetsResponse.class))),
            @ApiResponse(responseCode = "404", description = "Claim definition not found"),
            @ApiResponse(responseCode = "400", description = "Invalid request data or one or more claim set not found")
    })
    @PatchMapping("${app.claims.context-path}/claim-definitions/{claimDefinitionId}/claim-sets")
    @ResponseStatus(HttpStatus.OK)
    public ClaimDefinitionClaimSetsResponse patchClaimSets(
            @PathVariable UUID claimDefinitionId,
            @RequestBody @Valid PatchClaimSetsOnClaimDefinitionRequest request) {

        List<ClaimSetAssignmentCommand> assignments = request.getClaimSets() != null ? 
                request.getClaimSets().stream()
                        .map(dto -> new ClaimSetAssignmentCommand(dto.getClaimSetId(), dto.getEnforceUniqueness()))
                        .toList() : 
                List.of();

        PatchClaimSetsOnClaimDefinitionCommand command = new PatchClaimSetsOnClaimDefinitionCommand(
                claimDefinitionId,
                assignments
        );

        patchClaimSetsOnClaimDefinitionHandler.patchClaimSetsOnClaimDefinition(command);

        return new ClaimDefinitionClaimSetsResponse()
                .setClaimDefinitionId(claimDefinitionId)
                .setClaimSets(request.getClaimSets() != null ? request.getClaimSets() : List.of());
    }

}
