package com.vusecurity.auth.shared.config;

import java.util.function.Function;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.core.oidc.OidcUserInfo;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.server.authorization.JdbcOAuth2AuthorizationConsentService;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationConsentService;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationService;
import org.springframework.security.oauth2.server.authorization.client.RegisteredClientRepository;
import org.springframework.security.oauth2.server.authorization.config.annotation.web.configuration.OAuth2AuthorizationServerConfiguration;
import org.springframework.security.oauth2.server.authorization.config.annotation.web.configurers.OAuth2AuthorizationServerConfigurer;
import org.springframework.security.oauth2.server.authorization.oidc.authentication.OidcUserInfoAuthenticationContext;
import org.springframework.security.oauth2.server.authorization.settings.AuthorizationServerSettings;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.access.channel.ChannelProcessingFilter;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;
import org.springframework.security.web.session.HttpSessionEventPublisher;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.RSAKey;
import com.nimbusds.jose.jwk.source.JWKSource;
import com.nimbusds.jose.proc.SecurityContext;
import com.vusecurity.auth.authorization.application.service.MultitenantJdbcOAuth2AuthorizationService;
import com.vusecurity.auth.authorization.application.service.OAuth2RegisteredClientService;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.Oauth2RegisteredClientRepository;
import com.vusecurity.auth.shared.infrastructure.security.FilterMultitenantSecurity;
import com.vusecurity.auth.shared.infrastructure.security.mfa.BasicMultiFactorAuthenticationConverter;
import com.vusecurity.auth.shared.infrastructure.security.mfa.MultiFactorAuthenticationFilter;
import com.vusecurity.auth.shared.infrastructure.security.mfa.MultiFactorAuthenticationProvider;
import com.vusecurity.auth.shared.util.Jwks;
import com.vusecurity.multitenant.MultitenantConfiguration;
import com.vusecurity.multitenant.jpa.hibernate.DataSourceBasedMultiTenantConnectionProvider;
import com.vusecurity.multitenant.jpa.hibernate.MultitenantJdbcTemplate;

@Configuration
public class SecurityConfig {

        private final MultitenantConfiguration multitenantConfiguration;

        public SecurityConfig(MultitenantConfiguration multitenantConfiguration) {
                this.multitenantConfiguration = multitenantConfiguration;
        }

        @Bean
        public FilterMultitenantSecurity filterMultitenant() {
                return new FilterMultitenantSecurity(multitenantConfiguration);
        }

        @Bean
        public SecurityFilterChain defaultSecurityFilterChain(HttpSecurity http,
                        Function<OidcUserInfoAuthenticationContext, OidcUserInfo> userInfoMapper,
                        MultiFactorAuthenticationProvider multiFactorAuthenticationProvider) throws Exception {

                OAuth2AuthorizationServerConfigurer authorizationServerConfigurer = OAuth2AuthorizationServerConfigurer
                                .authorizationServer();

                AuthenticationManager authenticationManager = http.getSharedObject(AuthenticationManagerBuilder.class)
                                .authenticationProvider(multiFactorAuthenticationProvider)
                                .build();

                MultiFactorAuthenticationFilter multiFactorFilter = new MultiFactorAuthenticationFilter(
                                authenticationManager);

                BasicAuthenticationFilter basicAuthenticationFilter = new BasicAuthenticationFilter(authenticationManager);
                basicAuthenticationFilter.setAuthenticationConverter(new BasicMultiFactorAuthenticationConverter());

                http
                                .authorizeHttpRequests((authorize) -> authorize
                                                // Lista blanca de endpoints públicos
                                                .requestMatchers("/actuator/**").permitAll()
                                                .requestMatchers("/status", "/health").permitAll()
                                                .requestMatchers("/initialize").permitAll()
                                                .requestMatchers("/version").permitAll()
                                                .requestMatchers("/swagger-ui/**", "/v3/api-docs/**",
                                                                "/swagger-resources/**", "/webjars/**")
                                                .permitAll()
                                                .requestMatchers("/favicon.ico").permitAll()
                                                .requestMatchers("/error").permitAll()
                                                // OAuth2/OIDC endpoints públicos necesarios
                                                .requestMatchers("/.well-known/**").permitAll()
                                                .requestMatchers("/oauth2/**").permitAll()
                                                // Bloquear todo lo demás por defecto - requiere autenticación
                                                .anyRequest().authenticated())
                                // Desactiva la creación de sesiones
                                .sessionManagement(session -> session
                                                .sessionCreationPolicy(SessionCreationPolicy.NEVER))
                                // Desactiva Remember Me (evita la cookie remember-me)
                                .rememberMe(rememberMe -> rememberMe.disable())
                                // Desactiva la cabecera de caché de sesión
                                .headers(headers -> headers
                                                .cacheControl(cache -> cache.disable()))
                                // Desactiva el login por formulario y logout
                                .formLogin(form -> form.disable())
                                .logout(logout -> logout.disable())
                                .csrf(csrf -> csrf.disable()) // Deshabilita CSRF para llamadas API
                                .authenticationManager(authenticationManager)
                                .addFilterBefore(filterMultitenant(), ChannelProcessingFilter.class)
                                .addFilterAfter(basicAuthenticationFilter,
                                                FilterMultitenantSecurity.class)
                                .addFilterAfter(multiFactorFilter, FilterMultitenantSecurity.class)
                                // .securityMatcher(authorizationServerConfigurer.getEndpointsMatcher())
                                .with(authorizationServerConfigurer, (authorizationServer) -> authorizationServer
                                                .oidc((oidc) -> oidc
                                                                .userInfoEndpoint((userInfo) -> userInfo
                                                                                .userInfoMapper(userInfoMapper))));

                return http.build();
        }

        @Bean
        public AuthorizationServerSettings authorizationServerSettings() {
                return AuthorizationServerSettings.builder()
                                .build();
        }

        @Bean
        public JWKSource<SecurityContext> jwkSource() {
                RSAKey rsaKey = Jwks.generateRsa();
                JWKSet jwkSet = new JWKSet(rsaKey);
                return (jwkSelector, securityContext) -> jwkSelector.select(jwkSet);
        }

        @Bean
        public JwtDecoder jwtDecoder(JWKSource<SecurityContext> jwkSource) {
                return OAuth2AuthorizationServerConfiguration.jwtDecoder(jwkSource);
        }

    @Bean
    @ConditionalOnProperty(name = "app.multitenant.enabled", havingValue = "true", matchIfMissing = true)
    public JdbcTemplate multitenantJdbcTemplate(
            DataSourceBasedMultiTenantConnectionProvider dataSourceBasedMultiTenantConnectionProvider) {
        return new MultitenantJdbcTemplate(dataSourceBasedMultiTenantConnectionProvider);
    }

        @Bean
        public OAuth2RegisteredClientService registeredClientService(
                        Oauth2RegisteredClientRepository oauth2RegisteredClientRepository) {
                return new OAuth2RegisteredClientService(oauth2RegisteredClientRepository);
        }

        @Bean
        public OAuth2AuthorizationService authorizationService(JdbcTemplate multitenantJdbcTemplate,
                        RegisteredClientRepository registeredClientRepository, ObjectMapper securityObjectMapper) {
                return new MultitenantJdbcOAuth2AuthorizationService(multitenantJdbcTemplate,
                                registeredClientRepository, securityObjectMapper);
        }

        @Bean
        public OAuth2AuthorizationConsentService authorizationConsentService(JdbcTemplate multitenantJdbcTemplate,
                        RegisteredClientRepository registeredClientRepository) {
                return new JdbcOAuth2AuthorizationConsentService(multitenantJdbcTemplate, registeredClientRepository);
        }

        @Bean
        public HttpSessionEventPublisher httpSessionEventPublisher() {
                return new HttpSessionEventPublisher();
        }

        @Bean
        public PasswordEncoder passwordEncoder() {
                return new BCryptPasswordEncoder();
        }

}
