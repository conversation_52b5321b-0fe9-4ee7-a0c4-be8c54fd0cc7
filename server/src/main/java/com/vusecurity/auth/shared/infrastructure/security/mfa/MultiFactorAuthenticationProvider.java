package com.vusecurity.auth.shared.infrastructure.security.mfa;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

import com.vusecurity.auth.shared.infrastructure.security.oidc.AccountDetailService;
import com.vusecurity.auth.shared.infrastructure.security.oidc.AccountUserDetails;

import java.util.UUID;

/**
 * AuthenticationProvider that validates multi-factor authentication (accountId,
 * TOTP, password)
 * against the external factor server. This provider integrates with the
 * existing account-based
 * authentication system and follows the project's architecture patterns.
 */
@Component
public class MultiFactorAuthenticationProvider implements AuthenticationProvider {

    private static final Logger logger = LoggerFactory.getLogger(MultiFactorAuthenticationProvider.class);

    private final FactorServerClient factorServerClient;
    private final AccountDetailService accountDetailService;

    public MultiFactorAuthenticationProvider(FactorServerClient factorServerClient,
            UserDetailsService accountUserDetailsService) {
        this.factorServerClient = factorServerClient;
        this.accountDetailService = (AccountDetailService) accountUserDetailsService;
    }

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        logger.debug("Attempting multi-factor authentication");

        if (!(authentication instanceof MultiFactorAuthenticationToken)) {
            logger.debug("Authentication token is not MultiFactorAuthenticationToken");
            return null;
        }

        MultiFactorAuthenticationToken mfaToken = (MultiFactorAuthenticationToken) authentication;
        UUID accountId = mfaToken.getAccountId();
        String totp = mfaToken.getTotp();
        String password = mfaToken.getPassword();

        if (accountId == null || (totp == null && password == null)) {
            logger.debug("Missing required authentication factors");
            throw new BadCredentialsException("Missing required authentication factors");
        }

        try {
            logger.debug("Validating multi-factor authentication for account: {}", accountId);

            // Load user details to get business and channel information
            AccountUserDetails userDetails = loadUserDetails(accountId);

            // Extract business and channel IDs from user details
            String businessId = userDetails.getBusinessId().toString();
            String channelId = extractChannelId(userDetails);

            // Validate password first
            if (password != null) {
                boolean passwordValid = factorServerClient.validatePassword(accountId, password, businessId, channelId);
                if (!passwordValid) {
                    logger.debug("Password validation failed for account: {}", accountId);
                    throw new BadCredentialsException("Invalid password");
                }
            }

            // Only validate TOTP if password is valid
            if (totp != null) {
                boolean totpValid = factorServerClient.validateTotp(accountId, totp, businessId, channelId);
                if (!totpValid) {
                    logger.debug("TOTP validation failed for account: {}", accountId);
                    throw new BadCredentialsException("Invalid TOTP");
                }
            }

            logger.debug("Multi-factor authentication successful for account: {}", accountId);

            // Create successful authentication token
            MultiFactorAuthenticationToken successToken = new MultiFactorAuthenticationToken(
                    userDetails,
                    userDetails.getAuthorities());

            // Copy details from original authentication
            successToken.setDetails(authentication.getDetails());

            return successToken;

        } catch (UsernameNotFoundException e) {
            logger.debug("Account not found: {}", accountId);
            throw new BadCredentialsException("Invalid account");
        } catch (BadCredentialsException e) {
            // Re-throw BadCredentialsException as-is
            throw e;
        } catch (Exception e) {
            logger.error("Multi-factor authentication error for account: {}", accountId, e);
            throw new BadCredentialsException("Authentication failed");
        }
    }

    /**
     * Loads user details by account ID
     */
    private AccountUserDetails loadUserDetails(UUID accountId) throws UsernameNotFoundException {
        try {
            // Use account ID as username - AccountDetailService handles this
            return (AccountUserDetails) accountDetailService.loadUserByUsername(accountId.toString());
        } catch (ClassCastException e) {
            logger.error("UserDetails is not an instance of AccountUserDetails for account: {}", accountId);
            throw new BadCredentialsException("Invalid user details type");
        }
    }

    /**
     * Extracts channel ID from user details.
     * This method can be enhanced based on how channel information is stored in the
     * system.
     */
    private String extractChannelId(AccountUserDetails userDetails) {
        // For now, we'll use a default channel ID or extract from claims
        // This should be enhanced based on your business logic
        String channelId = userDetails.getClaimValue("channel_id");

        if (channelId == null) {
            // Use a default channel ID or derive from business context
            // This should be configured based on your requirements
            channelId = "D37EE3D4-22C6-41F7-819D-1464B3B9C454";
            logger.debug("Using default channel ID for account: {}", userDetails.getAccountId());
        }

        return channelId;
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return MultiFactorAuthenticationToken.class.isAssignableFrom(authentication);
    }
}
