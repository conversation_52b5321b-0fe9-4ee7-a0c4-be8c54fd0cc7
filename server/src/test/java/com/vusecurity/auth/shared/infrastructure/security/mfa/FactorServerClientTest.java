package com.vusecurity.auth.shared.infrastructure.security.mfa;

import com.vusecurity.auth.AppConfigProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.http.*;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class FactorServerClientTest {

    private RestTemplate restTemplate;
    private AppConfigProperties props;
    private AppConfigProperties.FactorsServerConfig fs;
    private AppConfigProperties.MultitenantConfig mt;

    private FactorServerClient client;

    @BeforeEach
    void setUp() {
        restTemplate = mock(RestTemplate.class);
        props = new AppConfigProperties();
        fs = new AppConfigProperties.FactorsServerConfig();
        fs.setUrl("http://factor-server");
        fs.setApikey("key-123");
        mt = new AppConfigProperties.MultitenantConfig();
        mt.setIdHeader("X-ORIGINAL-HOST");
        props.setFactorsServer(fs);
        props.setMultitenant(mt);

        client = new FactorServerClient(restTemplate, props);
    }

    @Test
    void validatePassword_success_returnsTrue_and_sends_expected_request() {
        UUID accountId = UUID.randomUUID();
        when(restTemplate.exchange(anyString(), eq(HttpMethod.POST), any(HttpEntity.class), eq(Void.class)))
                .thenReturn(new ResponseEntity<Void>(HttpStatus.OK));

        boolean result = client.validatePassword(accountId, "pwd", "biz", "chn");

        assertTrue(result);
        ArgumentCaptor<String> url = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<HttpEntity<?>> entity = ArgumentCaptor.forClass(HttpEntity.class);
        verify(restTemplate).exchange(url.capture(), eq(HttpMethod.POST), entity.capture(), eq(Void.class));
        assertTrue(url.getValue().endsWith("/api/v1/passwords/login"));
        HttpHeaders headers = entity.getValue().getHeaders();
        assertEquals(MediaType.APPLICATION_JSON, headers.getContentType());
        assertEquals("key-123", headers.getFirst("x-apikey"));
        assertEquals("biz", headers.getFirst("X-Business-Id"));
        assertEquals("chn", headers.getFirst("X-Channel-Id"));
    }

    @Test
    void validatePassword_4xx_returnsFalse() {
        when(restTemplate.exchange(anyString(), eq(HttpMethod.POST), any(HttpEntity.class), eq(Void.class)))
                .thenThrow(HttpClientErrorException.create(HttpStatus.UNAUTHORIZED, "", HttpHeaders.EMPTY, null, null));
        boolean result = client.validatePassword(UUID.randomUUID(), "pwd", "biz", "chn");
        assertFalse(result);
    }

    @Test
    void validateTotp_created_returnsTrueOnlyFor200() {
        when(restTemplate.exchange(contains("/otp/time/login"), eq(HttpMethod.POST), any(HttpEntity.class), eq(Void.class)))
                .thenReturn(new ResponseEntity<Void>(HttpStatus.OK));
        assertTrue(client.validateTotp(UUID.randomUUID(), "123456", "biz", "chn"));
    }

    @Test
    void createPassword_success_returnsTrueFor201() {
        when(restTemplate.exchange(contains("/api/v1/passwords"), eq(HttpMethod.POST), any(HttpEntity.class), eq(Void.class)))
                .thenReturn(new ResponseEntity<Void>(HttpStatus.CREATED));
        assertTrue(client.createPassword(UUID.randomUUID(), "pwd", "biz"));
    }

    @Test
    void createPassword_4xx_returnsFalse() {
        when(restTemplate.exchange(contains("/api/v1/passwords"), eq(HttpMethod.POST), any(HttpEntity.class), eq(Void.class)))
                .thenThrow(HttpClientErrorException.create(HttpStatus.BAD_REQUEST, "", HttpHeaders.EMPTY, null, null));
        assertFalse(client.createPassword(UUID.randomUUID(), "pwd", "biz"));
    }
}

