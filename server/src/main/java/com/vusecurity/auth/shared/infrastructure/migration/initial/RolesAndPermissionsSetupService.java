package com.vusecurity.auth.shared.infrastructure.migration.initial;

import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.PermissionJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.entity.RoleJpaEntity;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.PermissionRepository;
import com.vusecurity.auth.authorization.infrastructure.persistence.jpa.repository.RoleRepository;
import com.vusecurity.business.domain.Business;
import com.vusecurity.business.domain.repositories.BusinessRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class RolesAndPermissionsSetupService {

    private static final Logger logger = LoggerFactory.getLogger(RolesAndPermissionsSetupService.class);

    private final PermissionRepository permissionRepository;
    private final RoleRepository roleRepository;
    private final BusinessRepository businessRepository;

    public RolesAndPermissionsSetupService(PermissionRepository permissionRepository,
                                          RoleRepository roleRepository,
                                          BusinessRepository businessRepository) {
        this.permissionRepository = permissionRepository;
        this.roleRepository = roleRepository;
        this.businessRepository = businessRepository;
    }

    public void start() {
        logger.info("RolesAndPermissionsSetupService: Starting roles and permissions initialization...");
        logger.trace("Entering start method.");

        long permissionCount = permissionRepository.count();
        logger.debug("Current permission count: {}", permissionCount);

        if (permissionCount == 0) {
            logger.info("No permissions found in the repository. Starting roles and permissions setup.");

            Business business = businessRepository.findById(DataSeedConstants.SYSTEM_BUSINESS_ID)
                    .orElseThrow(() -> new IllegalStateException("Default business not found: " + DataSeedConstants.SYSTEM_BUSINESS_ID));
            logger.debug("Using system business: {}", business.getId());

            Set<PermissionJpaEntity> permissionsSet = getPermissions();
            logger.info("Creating {} permissions...", permissionsSet.size());

            // Save permissions one by one since repository doesn't have saveAll
            permissionsSet.forEach(permissionRepository::save);
            logger.info("Successfully saved {} permissions to the repository.", permissionsSet.size());

            // Create admin role using proper constructor
            RoleJpaEntity adminRole = new RoleJpaEntity(
                DataSeedConstants.ADMIN_ROLE_ID,
                business.getId(),
                "ROLE_ADMIN",
                "Admin role"
            );

            // Add permissions to admin role
            permissionsSet.forEach(adminRole::addPermission);
            roleRepository.save(adminRole);
            logger.info("Admin role created and saved with {} permissions: {}", permissionsSet.size(), adminRole.getId());

            // Create operator role using proper constructor
            RoleJpaEntity operatorRole = new RoleJpaEntity(
                DataSeedConstants.OPERATOR_ROLE_ID,
                business.getId(),
                "ROLE_OPERATOR",
                "Operator role"
            );

            // Add only read permissions to operator role
            Set<PermissionJpaEntity> readPermissions = getReadPermissions(permissionsSet);
            readPermissions.forEach(operatorRole::addPermission);
            roleRepository.save(operatorRole);
            logger.info("Operator role created and saved with {} read permissions: {}", readPermissions.size(), operatorRole.getId());

            logger.info("RolesAndPermissionsSetupService: Roles and permissions initialization completed successfully");

        } else {
            logger.info("Permissions already exist in the repository (count: {}). Skipping roles and permissions setup.", permissionCount);
        }

        logger.trace("Exiting start method.");
    }

    private static Set<PermissionJpaEntity> getReadPermissions(Set<PermissionJpaEntity> permissionsSet) {
        logger.trace("Filtering read permissions from the permissions set.");
        Set<PermissionJpaEntity> readPermissions = permissionsSet.stream()
                .filter(permission -> permission.getName().contains("read"))
                .collect(Collectors.toSet());
        logger.debug("Read permissions filtered: {}", readPermissions);
        return readPermissions;
    }

    private static Set<PermissionJpaEntity> getPermissions() {
        logger.trace("Creating permissions set.");
        Set<PermissionJpaEntity> permissionsSet = new HashSet<>();

        // Identity permissions
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_IDENTITY_CREATE_ID, "sys_identity:create", "Allow to create identities"));
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_IDENTITY_READ_ID, "sys_identity:read", "Allow to read identities"));
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_IDENTITY_UPDATE_ID, "sys_identity:update", "Allow to update identities"));
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_IDENTITY_DELETE_ID, "sys_identity:delete", "Allow to delete identities"));
        // Identity :all permission
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_IDENTITY_ALL_ID, "sys_identity:all", "Allow all identity operations"));

        // Account permissions
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_ACCOUNT_CREATE_ID, "sys_account:create", "Allow to create accounts entities"));
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_ACCOUNT_READ_ID, "sys_account:read", "Allow to read accounts entities"));
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_ACCOUNT_UPDATE_ID, "sys_account:update", "Allow to update accounts entities"));
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_ACCOUNT_DELETE_ID, "sys_account:delete", "Allow to delete accounts entities"));
        // Account :all permission
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_ACCOUNT_ALL_ID, "sys_account:all", "Allow all account operations"));

        // Business permissions
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_BUSINESS_CREATE_ID, "sys_business:create", "Allow to create business"));
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_BUSINESS_READ_ID, "sys_business:read", "Allow to read business"));
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_BUSINESS_UPDATE_ID, "sys_business:update", "Allow to update business"));
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_BUSINESS_DELETE_ID, "sys_business:delete", "Allow to delete business"));
        // Business :all permission
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_BUSINESS_ALL_ID, "sys_business:all", "Allow all business operations"));

        // Claim permissions
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_CLAIM_CREATE_ID, "sys_claim:create", "Allow to create claims"));
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_CLAIM_READ_ID, "sys_claim:read", "Allow to read claims"));
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_CLAIM_UPDATE_ID, "sys_claim:update", "Allow to update claims"));
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_CLAIM_DELETE_ID, "sys_claim:delete", "Allow to delete claims"));
        // Claim :all permission
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_CLAIM_ALL_ID, "sys_claim:all", "Allow all claim operations"));

        // Consent permissions
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_CONSENT_CREATE_ID, "sys_consent:create", "Allow to create consents"));
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_CONSENT_READ_ID, "sys_consent:read", "Allow to read consents"));
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_CONSENT_UPDATE_ID, "sys_consent:update", "Allow to update consents"));
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_CONSENT_DELETE_ID, "sys_consent:delete", "Allow to delete consents"));
        // Consent :all permission
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_CONSENT_ALL_ID, "sys_consent:all", "Allow all consent operations"));

        // Role permissions
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_ROLE_CREATE_ID, "sys_role:create", "Allow to create roles"));
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_ROLE_READ_ID, "sys_role:read", "Allow to read roles"));
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_ROLE_UPDATE_ID, "sys_role:update", "Allow to update roles"));
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_ROLE_DELETE_ID, "sys_role:delete", "Allow to delete roles"));
        // Role :all permission
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_ROLE_ALL_ID, "sys_role:all", "Allow all role operations"));

        // Permission permissions
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_PERMISSION_CREATE_ID, "sys_permission:create", "Allow to create permissions"));
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_PERMISSION_READ_ID, "sys_permission:read", "Allow to read permissions"));
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_PERMISSION_UPDATE_ID, "sys_permission:update", "Allow to update permissions"));
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_PERMISSION_DELETE_ID, "sys_permission:delete", "Allow to delete permissions"));
        // Permission :all permission
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_PERMISSION_ALL_ID, "sys_permission:all", "Allow all permission operations"));

        // Group permissions
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_GROUP_CREATE_ID, "sys_group:create", "Allow to create groups"));
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_GROUP_READ_ID, "sys_group:read", "Allow to read groups"));
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_GROUP_UPDATE_ID, "sys_group:update", "Allow to update groups"));
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_GROUP_DELETE_ID, "sys_group:delete", "Allow to delete groups"));
        // Group :all permission
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_GROUP_ALL_ID, "sys_group:all", "Allow all group operations"));

        // Channel permissions
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_CHANNEL_CREATE_ID, "sys_channel:create", "Allow to create channels"));
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_CHANNEL_READ_ID, "sys_channel:read", "Allow to read channels"));
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_CHANNEL_UPDATE_ID, "sys_channel:update", "Allow to update channels"));
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_CHANNEL_DELETE_ID, "sys_channel:delete", "Allow to delete channels"));
        // Channel :all permission
        permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_CHANNEL_ALL_ID, "sys_channel:all", "Allow all channel operations"));

    // Factor TOTP permissions
    permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_FACTOR_TOTP_CREATE_ID, "sys_factor_totp:create", "Allow to create TOTP factors"));
    permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_FACTOR_TOTP_READ_ID, "sys_factor_totp:read", "Allow to read TOTP factors"));
    permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_FACTOR_TOTP_UPDATE_ID, "sys_factor_totp:update", "Allow to update TOTP factors"));
    permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_FACTOR_TOTP_DELETE_ID, "sys_factor_totp:delete", "Allow to delete TOTP factors"));
    permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_FACTOR_TOTP_ALL_ID, "sys_factor_totp:all", "Allow all TOTP factor operations"));

    // Factor SMS permissions
    permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_FACTOR_SMS_CREATE_ID, "sys_factor_sms:create", "Allow to create SMS factors"));
    permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_FACTOR_SMS_READ_ID, "sys_factor_sms:read", "Allow to read SMS factors"));
    permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_FACTOR_SMS_UPDATE_ID, "sys_factor_sms:update", "Allow to update SMS factors"));
    permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_FACTOR_SMS_DELETE_ID, "sys_factor_sms:delete", "Allow to delete SMS factors"));
    permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_FACTOR_SMS_ALL_ID, "sys_factor_sms:all", "Allow all SMS factor operations"));

    // Factor Email permissions
    permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_FACTOR_EMAIL_CREATE_ID, "sys_factor_email:create", "Allow to create Email factors"));
    permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_FACTOR_EMAIL_READ_ID, "sys_factor_email:read", "Allow to read Email factors"));
    permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_FACTOR_EMAIL_UPDATE_ID, "sys_factor_email:update", "Allow to update Email factors"));
    permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_FACTOR_EMAIL_DELETE_ID, "sys_factor_email:delete", "Allow to delete Email factors"));
    permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_FACTOR_EMAIL_ALL_ID, "sys_factor_email:all", "Allow all Email factor operations"));

    // Factor Password permissions
    permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_FACTOR_PASSWORD_CREATE_ID, "sys_factor_password:create", "Allow to create Password factors"));
    permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_FACTOR_PASSWORD_READ_ID, "sys_factor_password:read", "Allow to read Password factors"));
    permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_FACTOR_PASSWORD_UPDATE_ID, "sys_factor_password:update", "Allow to update Password factors"));
    permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_FACTOR_PASSWORD_DELETE_ID, "sys_factor_password:delete", "Allow to delete Password factors"));
    permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_FACTOR_PASSWORD_ALL_ID, "sys_factor_password:all", "Allow all Password factor operations"));

    // Factor Policies permissions
    permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_FACTOR_POLICIES_CREATE_ID, "sys_factor_policies:create", "Allow to create Factor policies"));
    permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_FACTOR_POLICIES_READ_ID, "sys_factor_policies:read", "Allow to read Factor policies"));
    permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_FACTOR_POLICIES_UPDATE_ID, "sys_factor_policies:update", "Allow to update Factor policies"));
    permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_FACTOR_POLICIES_DELETE_ID, "sys_factor_policies:delete", "Allow to delete Factor policies"));
    permissionsSet.add(new PermissionJpaEntity(DataSeedConstants.PERMISSION_FACTOR_POLICIES_ALL_ID, "sys_factor_policies:all", "Allow all Factor policies operations"));

        logger.debug("Permissions set created: {}", permissionsSet);
        return permissionsSet;
    }
}
